"""Base models and types for the application."""

from datetime import date, datetime
from decimal import Decimal
from typing import Any, Dict, Generic, Optional, TypeVar
from uuid import UUID, uuid4

from pydantic import BaseModel, Field

# Type variable for generic pagination
T = TypeVar('T')


class TimestampMixin(BaseModel):
    """Mixin for models with timestamp fields."""
    
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class DynamoDBItem(BaseModel):
    """Base class for DynamoDB items with partition and sort keys."""
    
    pk: str = Field(..., description="Partition key")
    sk: str = Field(..., description="Sort key")
    
    def to_dynamodb_item(self) -> Dict[str, Any]:
        """Convert to DynamoDB item format."""
        item = self.dict()
        
        # Convert datetime objects to ISO strings
        for key, value in item.items():
            if isinstance(value, datetime):
                item[key] = value.isoformat()
            elif isinstance(value, date):
                item[key] = value.isoformat()
            elif isinstance(value, UUID):
                item[key] = str(value)
            elif isinstance(value, float):
                item[key] = Decimal(str(value))
        
        return item
    
    @classmethod
    def from_dynamodb_item(cls, item: Dict[str, Any]) -> "DynamoDBItem":
        """Create instance from DynamoDB item."""
        # Convert ISO strings back to datetime objects
        for key, value in item.items():
            if isinstance(value, str):
                # Try to parse as datetime
                try:
                    if "T" in value and ("Z" in value or "+" in value or value.endswith(":00")):
                        item[key] = datetime.fromisoformat(value.replace("Z", "+00:00"))
                except ValueError:
                    pass
        
        return cls(**item)


class PaginationParams(BaseModel):
    """Pagination parameters for list operations."""
    
    limit: int = Field(default=20, ge=1, le=100, description="Number of items to return")
    last_evaluated_key: Optional[str] = Field(
        default=None, description="Last evaluated key for pagination"
    )


class PaginatedResponse(BaseModel, Generic[T]):
    """Paginated response wrapper."""

    items: list[T] = Field(description="List of items")
    last_evaluated_key: Optional[str] = Field(
        default=None, description="Last evaluated key for next page"
    )
    has_more: bool = Field(description="Whether there are more items")
    total_count: Optional[int] = Field(default=None, description="Total count if available")
