"""User model and related schemas."""

from datetime import datetime
from typing import Optional
from uuid import UUID, uuid4

from pydantic import BaseModel, Field, validator

from .base import DynamoDBItem, TimestampMixin


class User(DynamoDBItem, TimestampMixin):
    """User model for DynamoDB storage."""
    
    # DynamoDB keys
    pk: str = Field(..., description="Partition key: USER#<user_id>")
    sk: str = Field(default="PROFILE", description="Sort key: PROFILE")
    
    # User fields
    id: UUID = Field(default_factory=uuid4, description="User ID")
    email: str = Field(..., description="User email (unique)")
    name: str = Field(..., description="User full name")
    picture_url: Optional[str] = Field(default=None, description="User profile picture URL")
    google_sub: str = Field(..., description="Google subject ID (unique)")
    last_login_at: Optional[datetime] = Field(default=None, description="Last login timestamp")
    
    @validator("pk", pre=True, always=True)
    def set_pk(cls, v, values):
        """Set partition key based on user ID."""
        if v and v.startswith("USER#"):
            return v
        # Get user_id from values, or generate a new one if not present
        user_id = values.get("id")
        if not user_id:
            user_id = uuid4()
            values["id"] = user_id
        return f"USER#{user_id}"
    
    @classmethod
    def create_pk(cls, user_id: UUID) -> str:
        """Create partition key for user."""
        return f"USER#{user_id}"
    
    def update_last_login(self) -> None:
        """Update last login timestamp."""
        self.last_login_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()


class UserCreate(BaseModel):
    """Schema for creating a new user."""
    
    email: str = Field(..., description="User email")
    name: str = Field(..., description="User full name")
    picture_url: Optional[str] = Field(default=None, description="User profile picture URL")
    google_sub: str = Field(..., description="Google subject ID")


class UserUpdate(BaseModel):
    """Schema for updating user information."""
    
    name: Optional[str] = Field(default=None, description="User full name")
    picture_url: Optional[str] = Field(default=None, description="User profile picture URL")


class UserResponse(BaseModel):
    """User response schema for API."""
    
    id: UUID = Field(..., description="User ID")
    email: str = Field(..., description="User email")
    name: str = Field(..., description="User full name")
    picture_url: Optional[str] = Field(default=None, description="User profile picture URL")
    created_at: datetime = Field(..., description="User creation timestamp")
    updated_at: datetime = Field(..., description="User last update timestamp")
    last_login_at: Optional[datetime] = Field(default=None, description="Last login timestamp")
    
    @classmethod
    def from_user(cls, user: User) -> "UserResponse":
        """Create response from user model."""
        return cls(
            id=user.id,
            email=user.email,
            name=user.name,
            picture_url=user.picture_url,
            created_at=user.created_at,
            updated_at=user.updated_at,
            last_login_at=user.last_login_at,
        )
