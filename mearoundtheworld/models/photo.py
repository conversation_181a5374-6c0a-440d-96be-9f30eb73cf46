"""Photo model and related schemas."""

from datetime import date, datetime
from typing import Optional
from uuid import UUID, uuid4

from pydantic import BaseModel, Field, validator

from .base import DynamoDBItem, TimestampMixin


class Photo(DynamoDBItem, TimestampMixin):
    """Photo model for DynamoDB storage."""
    
    # DynamoDB keys
    pk: str = Field(..., description="Partition key: USER#<user_id>")
    sk: str = Field(..., description="Sort key: PHOTO#<photo_id>")
    
    # Photo fields
    photo_id: UUID = Field(default_factory=uuid4, description="Photo ID")
    user_id: UUID = Field(..., description="User ID who owns the photo")
    place_name: str = Field(..., description="Name of the place")
    country_name: Optional[str] = Field(default=None, description="Country name")
    object_key: str = Field(..., description="S3 object key")
    s3_url: Optional[str] = Field(default=None, description="S3 URL (can be derived)")
    latitude: float = Field(..., description="Latitude coordinate", ge=-90, le=90)
    longitude: float = Field(..., description="Longitude coordinate", ge=-180, le=180)
    brief_description: str = Field(
        ..., description="Brief description of the photo", max_length=500
    )
    trip_date: date = Field(..., description="Date when the photo was taken")
    
    @validator("pk", pre=True, always=True)
    def set_pk(cls, v, values):
        """Set partition key based on user ID."""
        if v and v.startswith("USER#"):
            return v
        user_id = values.get("user_id")
        if user_id:
            return f"USER#{user_id}"
        raise ValueError("user_id is required to set pk")
    
    @validator("sk", pre=True, always=True)
    def set_sk(cls, v, values):
        """Set sort key based on photo ID."""
        if v and v.startswith("PHOTO#"):
            return v
        photo_id = values.get("photo_id") or uuid4()
        return f"PHOTO#{photo_id}"
    
    @classmethod
    def create_pk(cls, user_id: UUID) -> str:
        """Create partition key for user's photos."""
        return f"USER#{user_id}"
    
    @classmethod
    def create_sk(cls, photo_id: UUID) -> str:
        """Create sort key for photo."""
        return f"PHOTO#{photo_id}"


class PhotoCreate(BaseModel):
    """Schema for creating a new photo."""
    
    place_name: str = Field(..., description="Name of the place")
    country_name: Optional[str] = Field(default=None, description="Country name")
    object_key: str = Field(..., description="S3 object key")
    latitude: float = Field(..., description="Latitude coordinate", ge=-90, le=90)
    longitude: float = Field(..., description="Longitude coordinate", ge=-180, le=180)
    brief_description: str = Field(
        ..., description="Brief description of the photo", max_length=500
    )
    trip_date: date = Field(..., description="Date when the photo was taken")


class PhotoUpdate(BaseModel):
    """Schema for updating photo information."""
    
    place_name: Optional[str] = Field(default=None, description="Name of the place")
    country_name: Optional[str] = Field(default=None, description="Country name")
    latitude: Optional[float] = Field(default=None, description="Latitude coordinate", ge=-90, le=90)
    longitude: Optional[float] = Field(default=None, description="Longitude coordinate", ge=-180, le=180)
    brief_description: Optional[str] = Field(
        default=None, description="Brief description of the photo", max_length=500
    )
    trip_date: Optional[date] = Field(default=None, description="Date when the photo was taken")


class PhotoResponse(BaseModel):
    """Photo response schema for API."""
    
    photo_id: UUID = Field(..., description="Photo ID")
    user_id: UUID = Field(..., description="User ID who owns the photo")
    place_name: str = Field(..., description="Name of the place")
    country_name: Optional[str] = Field(default=None, description="Country name")
    object_key: str = Field(..., description="S3 object key")
    s3_url: Optional[str] = Field(default=None, description="S3 URL")
    latitude: float = Field(..., description="Latitude coordinate")
    longitude: float = Field(..., description="Longitude coordinate")
    brief_description: str = Field(..., description="Brief description of the photo")
    trip_date: date = Field(..., description="Date when the photo was taken")
    created_at: datetime = Field(..., description="Photo creation timestamp")
    updated_at: datetime = Field(..., description="Photo last update timestamp")
    
    @classmethod
    def from_photo(cls, photo: Photo, s3_url: Optional[str] = None) -> "PhotoResponse":
        """Create response from photo model."""
        return cls(
            photo_id=photo.photo_id,
            user_id=photo.user_id,
            place_name=photo.place_name,
            country_name=photo.country_name,
            object_key=photo.object_key,
            s3_url=s3_url or photo.s3_url,
            latitude=photo.latitude,
            longitude=photo.longitude,
            brief_description=photo.brief_description,
            trip_date=photo.trip_date,
            created_at=photo.created_at,
            updated_at=photo.updated_at,
        )


class UploadUrlRequest(BaseModel):
    """Request schema for getting upload URL."""
    
    filename: str = Field(..., description="Original filename")
    content_type: str = Field(..., description="Content type of the file")


class UploadUrlResponse(BaseModel):
    """Response schema for upload URL."""
    
    upload_url: str = Field(..., description="Pre-signed upload URL")
    object_key: str = Field(..., description="S3 object key to use when creating photo")
    expires_in: int = Field(..., description="URL expiration time in seconds")
