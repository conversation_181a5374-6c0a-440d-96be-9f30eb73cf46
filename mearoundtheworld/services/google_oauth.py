"""Google OAuth service for authentication."""

from typing import Dict, Optional
from urllib.parse import urlencode

import httpx
from pydantic import BaseModel

from mearoundtheworld.config.logging import get_logger
from mearoundtheworld.config.settings import settings

logger = get_logger(__name__)


class GoogleUserInfo(BaseModel):
    """Google user information from OAuth."""
    
    id: str  # Google subject ID
    email: str
    name: str
    picture: Optional[str] = None
    verified_email: bool = False


class GoogleTokenResponse(BaseModel):
    """Google OAuth token response."""
    
    access_token: str
    token_type: str
    expires_in: int
    refresh_token: Optional[str] = None
    scope: str
    id_token: Optional[str] = None


class GoogleOAuthService:
    """Service for Google OAuth 2.0 operations."""
    
    GOOGLE_AUTH_URL = "https://accounts.google.com/o/oauth2/v2/auth"
    GOOGLE_TOKEN_URL = "https://oauth2.googleapis.com/token"
    GOOGLE_USERINFO_URL = "https://www.googleapis.com/oauth2/v2/userinfo"
    
    def __init__(self) -> None:
        """Initialize Google OAuth service."""
        self.client_id = settings.google_client_id
        self.client_secret = settings.google_client_secret
        self.redirect_uri = settings.google_redirect_uri
        
        logger.info("Google OAuth service initialized")
    
    def get_authorization_url(self, state: Optional[str] = None) -> str:
        """Generate Google OAuth authorization URL."""
        params = {
            "client_id": self.client_id,
            "redirect_uri": self.redirect_uri,
            "scope": "openid email profile",
            "response_type": "code",
            "access_type": "offline",
            "prompt": "consent",
        }
        
        if state:
            params["state"] = state
        
        url = f"{self.GOOGLE_AUTH_URL}?{urlencode(params)}"
        logger.debug("Authorization URL generated", url=url)
        return url
    
    async def exchange_code_for_tokens(self, code: str) -> Optional[GoogleTokenResponse]:
        """Exchange authorization code for access tokens."""
        try:
            data = {
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "code": code,
                "grant_type": "authorization_code",
                "redirect_uri": self.redirect_uri,
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.GOOGLE_TOKEN_URL,
                    data=data,
                    headers={"Content-Type": "application/x-www-form-urlencoded"},
                )
                
                if response.status_code != 200:
                    logger.error("Token exchange failed", status_code=response.status_code, response=response.text)
                    return None
                
                token_data = response.json()
                logger.debug("Tokens exchanged successfully")
                return GoogleTokenResponse(**token_data)
                
        except Exception as e:
            logger.error("Failed to exchange code for tokens", error=str(e))
            return None
    
    async def get_user_info(self, access_token: str) -> Optional[GoogleUserInfo]:
        """Get user information from Google using access token."""
        try:
            headers = {"Authorization": f"Bearer {access_token}"}
            
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    self.GOOGLE_USERINFO_URL,
                    headers=headers,
                )
                
                if response.status_code != 200:
                    logger.error("User info request failed", status_code=response.status_code, response=response.text)
                    return None
                
                user_data = response.json()
                logger.debug("User info retrieved", email=user_data.get("email"))
                return GoogleUserInfo(**user_data)
                
        except Exception as e:
            logger.error("Failed to get user info", error=str(e))
            return None
    
    async def revoke_token(self, token: str) -> bool:
        """Revoke Google access token."""
        try:
            revoke_url = f"https://oauth2.googleapis.com/revoke?token={token}"
            
            async with httpx.AsyncClient() as client:
                response = await client.post(revoke_url)
                
                success = response.status_code == 200
                if success:
                    logger.debug("Token revoked successfully")
                else:
                    logger.warning("Token revocation failed", status_code=response.status_code)
                
                return success
                
        except Exception as e:
            logger.error("Failed to revoke token", error=str(e))
            return False
    
    async def refresh_access_token(self, refresh_token: str) -> Optional[GoogleTokenResponse]:
        """Refresh Google access token using refresh token."""
        try:
            data = {
                "client_id": self.client_id,
                "client_secret": self.client_secret,
                "refresh_token": refresh_token,
                "grant_type": "refresh_token",
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.GOOGLE_TOKEN_URL,
                    data=data,
                    headers={"Content-Type": "application/x-www-form-urlencoded"},
                )
                
                if response.status_code != 200:
                    logger.error("Token refresh failed", status_code=response.status_code, response=response.text)
                    return None
                
                token_data = response.json()
                logger.debug("Access token refreshed")
                return GoogleTokenResponse(**token_data)
                
        except Exception as e:
            logger.error("Failed to refresh access token", error=str(e))
            return None


# Global Google OAuth service instance
google_oauth_service = GoogleOAuthService()
