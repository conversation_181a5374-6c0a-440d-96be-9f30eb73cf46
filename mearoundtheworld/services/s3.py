"""S3 service for file storage and pre-signed URLs."""

import mimetypes
from typing import Optional
from uuid import UUID, uuid4

import boto3
from botocore.exceptions import ClientError

from mearoundtheworld.config.logging import get_logger
from mearoundtheworld.config.settings import settings

logger = get_logger(__name__)


class S3Service:
    """Service for S3 operations."""
    
    def __init__(self) -> None:
        """Initialize S3 client."""
        if settings.debug:
            logger.info("Initializing S3 client for localstack", endpoint_url=settings.aws_endpoint_url)
            self._s3_client = boto3.client(
                "s3",
                region_name=settings.aws_region,
                endpoint_url=settings.aws_endpoint_url,
                aws_access_key_id=settings.aws_access_key_id,
                aws_secret_access_key=settings.aws_secret_access_key,
            )
        else:
            self._s3_client = boto3.client(
                "s3",
                region_name=settings.aws_region
            )
        self.bucket_name = settings.s3_bucket_name
        self.presigned_url_expire_seconds = settings.s3_presigned_url_expire_seconds
        
        logger.info("S3 service initialized", bucket=self.bucket_name)
    
    def generate_object_key(self, user_id: UUID, filename: str) -> str:
        """Generate unique object key for user's photo.
        
        Args:
            user_id: User ID
            filename: Original filename
            
        Returns:
            Unique object key
        """
        # Extract file extension
        file_extension = ""
        if "." in filename:
            file_extension = filename.split(".")[-1].lower()
        
        # Generate unique key: users/{user_id}/photos/{uuid}.{ext}
        photo_id = uuid4()
        if file_extension:
            object_key = f"users/{user_id}/photos/{photo_id}.{file_extension}"
        else:
            object_key = f"users/{user_id}/photos/{photo_id}"
        
        return object_key
    
    def generate_presigned_upload_url(
        self, 
        object_key: str, 
        content_type: Optional[str] = None,
        expires_in: Optional[int] = None
    ) -> tuple[str, dict]:
        """Generate pre-signed URL for uploading a file.
        
        Args:
            object_key: S3 object key
            content_type: Content type of the file
            expires_in: URL expiration time in seconds
            
        Returns:
            Pre-signed upload URL
        """
        try:
            expires_in = expires_in or self.presigned_url_expire_seconds
            
            # Prepare conditions for the upload
            conditions = []
            fields = {}
            
            if content_type:
                conditions.append({"Content-Type": content_type})
                fields["Content-Type"] = content_type
            
            # Generate pre-signed POST URL
            response = self._s3_client.generate_presigned_post(
                Bucket=self.bucket_name,
                Key=object_key,
                Fields=fields,
                Conditions=conditions,
                ExpiresIn=expires_in
            )
            
            logger.debug("Pre-signed upload URL generated", object_key=object_key)
            logger.error(response)
            return response["url"], response["fields"]
            
        except Exception as e:
            logger.error("Failed to generate pre-signed upload URL", error=str(e), object_key=object_key)
            raise
    
    def generate_presigned_download_url(
        self, 
        object_key: str, 
        expires_in: Optional[int] = None
    ) -> str:
        """Generate pre-signed URL for downloading a file.
        
        Args:
            object_key: S3 object key
            expires_in: URL expiration time in seconds
            
        Returns:
            Pre-signed download URL
        """
        try:
            expires_in = expires_in or self.presigned_url_expire_seconds
            
            url = self._s3_client.generate_presigned_url(
                "get_object",
                Params={"Bucket": self.bucket_name, "Key": object_key},
                ExpiresIn=expires_in
            )
            
            logger.debug("Pre-signed download URL generated", object_key=object_key)
            return url
            
        except Exception as e:
            logger.error("Failed to generate pre-signed download URL", error=str(e), object_key=object_key)
            raise
    
    async def delete_object(self, object_key: str) -> bool:
        """Delete an object from S3.
        
        Args:
            object_key: S3 object key
            
        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            self._s3_client.delete_object(Bucket=self.bucket_name, Key=object_key)
            logger.info("Object deleted", object_key=object_key)
            return True
            
        except ClientError as e:
            logger.error("Failed to delete object", error=str(e), object_key=object_key)
            return False
    
    async def object_exists(self, object_key: str) -> bool:
        """Check if an object exists in S3.
        
        Args:
            object_key: S3 object key
            
        Returns:
            True if object exists, False otherwise
        """
        try:
            self._s3_client.head_object(Bucket=self.bucket_name, Key=object_key)
            return True
            
        except ClientError as e:
            if e.response["Error"]["Code"] == "404":
                return False
            logger.error("Failed to check object existence", error=str(e), object_key=object_key)
            raise
    
    def get_object_url(self, object_key: str) -> str:
        """Get public URL for an object (for public buckets).
        
        Args:
            object_key: S3 object key
            
        Returns:
            Object URL
        """
        if settings.aws_endpoint_url:
            # LocalStack or custom endpoint
            return f"{settings.aws_endpoint_url}/{self.bucket_name}/{object_key}"
        else:
            # AWS S3
            return f"https://{self.bucket_name}.s3.{settings.aws_region}.amazonaws.com/{object_key}"
    
    def validate_content_type(self, filename: str, content_type: str) -> bool:
        """Validate content type against filename.
        
        Args:
            filename: Original filename
            content_type: Provided content type
            
        Returns:
            True if content type is valid for the filename
        """
        # Get expected content type from filename
        expected_type, _ = mimetypes.guess_type(filename)
        
        # Allow if we can't determine the type
        if not expected_type:
            return True
        
        # Check if provided type matches expected type
        return content_type.lower() == expected_type.lower()
    
    def is_allowed_file_type(self, content_type: str) -> bool:
        """Check if file type is allowed for photo uploads.
        
        Args:
            content_type: Content type to check
            
        Returns:
            True if file type is allowed
        """
        allowed_types = {
            "image/jpeg",
            "image/jpg", 
            "image/png",
            "image/gif",
            "image/webp",
        }
        
        return content_type.lower() in allowed_types


# Global S3 service instance
s3_service = S3Service()
