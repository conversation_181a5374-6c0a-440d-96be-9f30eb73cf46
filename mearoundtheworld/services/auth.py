"""Authentication service for JWT token management."""

from datetime import datetime, timedelta
from typing import Any, Dict, Optional
from uuid import UUID

from jose import JWTError, jwt
from pydantic import BaseModel

from mearoundtheworld.config.logging import get_logger
from mearoundtheworld.config.settings import settings

logger = get_logger(__name__)


class TokenData(BaseModel):
    """Token data structure."""
    
    user_id: UUID
    email: str
    token_type: str  # "access" or "refresh"
    exp: datetime
    iat: datetime


class TokenPair(BaseModel):
    """Access and refresh token pair."""
    
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int  # Access token expiration in seconds


class AuthService:
    """Service for JWT authentication operations."""
    
    def __init__(self) -> None:
        """Initialize auth service."""
        self.secret_key = settings.jwt_secret_key
        self.algorithm = settings.jwt_algorithm
        self.access_token_expire_minutes = settings.jwt_access_token_expire_minutes
        self.refresh_token_expire_days = settings.jwt_refresh_token_expire_days
        
        logger.info("Auth service initialized", algorithm=self.algorithm)
    
    def create_access_token(self, user_id: UUID, email: str) -> str:
        """Create JWT access token."""
        now = datetime.utcnow()
        expire = now + timedelta(minutes=self.access_token_expire_minutes)
        
        payload = {
            "user_id": str(user_id),
            "email": email,
            "token_type": "access",
            "exp": expire,
            "iat": now,
        }
        
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        logger.debug("Access token created", user_id=user_id, expires_at=expire)
        return token
    
    def create_refresh_token(self, user_id: UUID, email: str) -> str:
        """Create JWT refresh token."""
        now = datetime.utcnow()
        expire = now + timedelta(days=self.refresh_token_expire_days)
        
        payload = {
            "user_id": str(user_id),
            "email": email,
            "token_type": "refresh",
            "exp": expire,
            "iat": now,
        }
        
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        logger.debug("Refresh token created", user_id=user_id, expires_at=expire)
        return token
    
    def create_token_pair(self, user_id: UUID, email: str) -> TokenPair:
        """Create access and refresh token pair."""
        access_token = self.create_access_token(user_id, email)
        refresh_token = self.create_refresh_token(user_id, email)
        
        return TokenPair(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=self.access_token_expire_minutes * 60,
        )
    
    def verify_token(self, token: str, expected_type: str = "access") -> Optional[TokenData]:
        """Verify and decode JWT token."""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # Validate token type
            token_type = payload.get("token_type")
            if token_type != expected_type:
                logger.warning("Invalid token type", expected=expected_type, actual=token_type)
                return None
            
            # Extract token data
            user_id = UUID(payload.get("user_id"))
            email = payload.get("email")
            exp = datetime.fromtimestamp(payload.get("exp"))
            iat = datetime.fromtimestamp(payload.get("iat"))
            
            token_data = TokenData(
                user_id=user_id,
                email=email,
                token_type=token_type,
                exp=exp,
                iat=iat,
            )
            
            logger.debug("Token verified", user_id=user_id, token_type=token_type)
            return token_data
            
        except JWTError as e:
            logger.warning("JWT verification failed", error=str(e))
            return None
        except Exception as e:
            logger.error("Token verification error", error=str(e))
            return None
    
    def refresh_access_token(self, refresh_token: str) -> Optional[str]:
        """Create new access token from refresh token."""
        token_data = self.verify_token(refresh_token, "refresh")
        if not token_data:
            return None
        
        # Check if refresh token is still valid
        if token_data.exp < datetime.utcnow():
            logger.warning("Refresh token expired", user_id=token_data.user_id)
            return None
        
        # Create new access token
        access_token = self.create_access_token(token_data.user_id, token_data.email)
        logger.info("Access token refreshed", user_id=token_data.user_id)
        return access_token
    
    def extract_token_from_header(self, authorization: str) -> Optional[str]:
        """Extract token from Authorization header."""
        if not authorization:
            return None
        
        parts = authorization.split()
        if len(parts) != 2 or parts[0].lower() != "bearer":
            logger.warning("Invalid authorization header format")
            return None
        
        return parts[1]


# Global auth service instance
_auth_service: Optional[AuthService] = None


def get_auth_service() -> AuthService:
    """Get or create the global auth service instance."""
    global _auth_service
    if _auth_service is None:
        _auth_service = AuthService()
    return _auth_service


# For backward compatibility
auth_service = get_auth_service()
