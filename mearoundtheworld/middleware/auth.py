"""Authentication middleware for Litestar."""

from typing import Any, Dict, Optional
from uuid import UUID

from litestar import Request
from litestar.exceptions import NotAuthorizedException, HTTPException
from litestar.middleware.base import AbstractMiddleware
from litestar.status_codes import HTTP_401_UNAUTHORIZED

from mearoundtheworld.config.logging import get_logger
from mearoundtheworld.services.auth import auth_service

logger = get_logger(__name__)


class AuthenticationMiddleware(AbstractMiddleware):
    """Middleware to handle JWT authentication."""
    
    # Routes that don't require authentication
    EXCLUDED_PATHS = {
        "/health",
        "/docs",
        "/schema",
        "/auth/login",
        "/auth/callback",
        "/auth/refresh",
        "/auth/mock-login",
    }
    
    async def __call__(self, scope: Dict[str, Any], receive: Any, send: Any) -> None:
        """Process request through authentication middleware."""
        request = Request(scope)
        
        # Skip authentication for excluded paths
        if self._should_skip_auth(request.url.path):
            await self.app(scope, receive, send)
            return
        
        # Extract and verify token
        token = self._extract_token(request)
        if not token:
            logger.warning("No token provided", path=request.url.path)
            raise HTTPException(
                status_code=HTTP_401_UNAUTHORIZED,
                detail="Authentication required",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        # Verify token
        token_data = auth_service.verify_token(token, "access")
        if not token_data:
            logger.warning("Invalid token", path=request.url.path)
            raise HTTPException(
                status_code=HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired token",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        # Add user info to request state
        scope["state"] = scope.get("state", {})
        scope["state"]["user_id"] = token_data.user_id
        scope["state"]["user_email"] = token_data.email
        
        logger.debug("Request authenticated", user_id=token_data.user_id, path=request.url.path)
        
        await self.app(scope, receive, send)
    
    def _should_skip_auth(self, path: str) -> bool:
        """Check if path should skip authentication."""
        # Exact match
        if path in self.EXCLUDED_PATHS:
            return True
        
        # Prefix match for docs and static files
        if path.startswith(("/docs", "/redoc", "/openapi", "/static")):
            return True
        
        return False
    
    def _extract_token(self, request: Request) -> Optional[str]:
        """Extract JWT token from request."""
        authorization = request.headers.get("authorization")
        return auth_service.extract_token_from_header(authorization)


def get_current_user_id(request: Request) -> UUID:
    """Get current user ID from request state."""
    user_id = request.state.get("user_id")
    if not user_id:
        logger.error("User ID not found in request state")
        raise NotAuthorizedException("User not authenticated")
    
    return user_id


def get_current_user_email(request: Request) -> str:
    """Get current user email from request state."""
    user_email = request.state.get("user_email")
    if not user_email:
        logger.error("User email not found in request state")
        raise NotAuthorizedException("User not authenticated")
    
    return user_email
