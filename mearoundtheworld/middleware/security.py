"""Security middleware for headers and CORS."""

from typing import Any, Dict

from litestar import Request, Response
from litestar.middleware.base import AbstractMiddleware

from mearoundtheworld.config.logging import get_logger
from mearoundtheworld.config.settings import settings

logger = get_logger(__name__)


class SecurityHeadersMiddleware(AbstractMiddleware):
    """Middleware to add security headers to responses."""
    
    async def __call__(self, scope: Dict[str, Any], receive: Any, send: Any) -> None:
        """Add security headers to response."""
        
        async def send_wrapper(message: Dict[str, Any]) -> None:
            """Wrapper to add headers to response."""
            if message["type"] == "http.response.start":
                headers = dict(message.get("headers", []))
                
                # Add security headers
                security_headers = {
                    b"x-content-type-options": b"nosniff",
                    b"x-frame-options": b"DENY",
                    b"x-xss-protection": b"1; mode=block",
                    b"referrer-policy": b"strict-origin-when-cross-origin",
                    b"permissions-policy": b"geolocation=(), microphone=(), camera=()",
                }
                
                # Add Content Security Policy
                if not settings.debug:
                    csp = (
                        "default-src 'self'; "
                        "script-src 'self' 'unsafe-inline'; "
                        "style-src 'self' 'unsafe-inline'; "
                        "img-src 'self' data: https:; "
                        "font-src 'self'; "
                        "connect-src 'self'; "
                        "frame-ancestors 'none';"
                    )
                    security_headers[b"content-security-policy"] = csp.encode()
                
                # Update headers
                for key, value in security_headers.items():
                    headers[key] = value
                
                # Convert back to list format
                message["headers"] = list(headers.items())
            
            await send(message)
        
        await self.app(scope, receive, send_wrapper)


class CORSMiddleware(AbstractMiddleware):
    """Custom CORS middleware."""
    
    def __init__(self, app: Any) -> None:
        """Initialize CORS middleware."""
        super().__init__(app)
        self.allowed_origins = set(settings.cors_allowed_origins)
        self.allowed_methods = set(settings.cors_allowed_methods)
        self.allowed_headers = set(settings.cors_allowed_headers)
        
        logger.info("CORS middleware initialized", 
                   origins=settings.cors_allowed_origins,
                   methods=settings.cors_allowed_methods)
    
    async def __call__(self, scope: Dict[str, Any], receive: Any, send: Any) -> None:
        """Handle CORS for requests."""
        request = Request(scope)
        origin = request.headers.get("origin")
        
        # Handle preflight requests
        if request.method == "OPTIONS":
            await self._handle_preflight(scope, receive, send, origin)
            return
        
        # Handle actual requests
        async def send_wrapper(message: Dict[str, Any]) -> None:
            """Add CORS headers to response."""
            if message["type"] == "http.response.start":
                headers = dict(message.get("headers", []))
                
                # Add CORS headers if origin is allowed
                if self._is_origin_allowed(origin):
                    cors_headers = {
                        b"access-control-allow-origin": origin.encode() if origin else b"*",
                        b"access-control-allow-credentials": b"true",
                        b"access-control-expose-headers": b"*",
                    }
                    
                    for key, value in cors_headers.items():
                        headers[key] = value
                
                message["headers"] = list(headers.items())
            
            await send(message)
        
        await self.app(scope, receive, send_wrapper)
    
    async def _handle_preflight(
        self, 
        scope: Dict[str, Any], 
        receive: Any, 
        send: Any, 
        origin: str
    ) -> None:
        """Handle CORS preflight requests."""
        request = Request(scope)
        
        # Check if origin is allowed
        if not self._is_origin_allowed(origin):
            logger.warning("CORS preflight rejected", origin=origin)
            response = Response(content="", status_code=403)
            await response(scope, receive, send)
            return
        
        # Get requested method and headers
        requested_method = request.headers.get("access-control-request-method", "")
        requested_headers = request.headers.get("access-control-request-headers", "")
        
        # Check if method is allowed
        if requested_method and requested_method not in self.allowed_methods:
            logger.warning("CORS method not allowed", method=requested_method, origin=origin)
            response = Response(content="", status_code=405)
            await response(scope, receive, send)
            return
        
        # Prepare CORS headers
        cors_headers = {
            "Access-Control-Allow-Origin": origin or "*",
            "Access-Control-Allow-Methods": ", ".join(self.allowed_methods),
            "Access-Control-Allow-Headers": ", ".join(self.allowed_headers),
            "Access-Control-Allow-Credentials": "true",
            "Access-Control-Max-Age": "86400",  # 24 hours
        }
        
        logger.debug("CORS preflight approved", origin=origin, method=requested_method)
        
        response = Response(
            content="",
            status_code=200,
            headers=cors_headers
        )
        await response(scope, receive, send)
    
    def _is_origin_allowed(self, origin: str) -> bool:
        """Check if origin is allowed."""
        if not origin:
            return True  # Allow requests without origin (e.g., mobile apps)
        
        # Check exact match
        if origin in self.allowed_origins:
            return True
        
        # Check wildcard
        if "*" in self.allowed_origins:
            return True
        
        # Check if any allowed origin is a wildcard pattern
        for allowed_origin in self.allowed_origins:
            if allowed_origin.startswith("*."):
                domain = allowed_origin[2:]  # Remove "*."
                if origin.endswith(f".{domain}") or origin == domain:
                    return True
        
        return False


class RateLimitMiddleware(AbstractMiddleware):
    """Simple rate limiting middleware (in-memory)."""
    
    def __init__(self, app: Any, requests_per_minute: int = 60) -> None:
        """Initialize rate limit middleware."""
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.request_counts: Dict[str, Dict[str, int]] = {}
        
        logger.info("Rate limit middleware initialized", rpm=requests_per_minute)
    
    async def __call__(self, scope: Dict[str, Any], receive: Any, send: Any) -> None:
        """Apply rate limiting."""
        request = Request(scope)
        
        # Get client identifier (IP address)
        client_ip = self._get_client_ip(request)
        
        # Check rate limit
        if self._is_rate_limited(client_ip):
            logger.warning("Rate limit exceeded", client_ip=client_ip)
            response = Response(
                content={"detail": "Rate limit exceeded"},
                status_code=429,
                headers={"Retry-After": "60"}
            )
            await response(scope, receive, send)
            return
        
        await self.app(scope, receive, send)
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address."""
        # Check for forwarded headers first
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fallback to client address
        client = request.scope.get("client")
        return client[0] if client else "unknown"
    
    def _is_rate_limited(self, client_ip: str) -> bool:
        """Check if client is rate limited."""
        import time
        
        current_minute = int(time.time() // 60)
        
        if client_ip not in self.request_counts:
            self.request_counts[client_ip] = {}
        
        client_requests = self.request_counts[client_ip]
        
        # Clean old entries
        for minute in list(client_requests.keys()):
            if minute < current_minute - 1:  # Keep only current and previous minute
                del client_requests[minute]
        
        # Count requests in current minute
        current_count = client_requests.get(current_minute, 0)
        
        if current_count >= self.requests_per_minute:
            return True
        
        # Increment counter
        client_requests[current_minute] = current_count + 1
        return False
