"""Structured logging configuration using structlog."""

import logging
import sys
from typing import Any, Dict

import structlog
from structlog.types import Processor

from .settings import settings


def configure_logging() -> None:
    """Configure structured logging with JSON output."""
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.log_level),
    )
    
    # Configure structlog processors
    processors: list[Processor] = [
        # Add timestamp
        structlog.stdlib.add_log_level,
        structlog.stdlib.add_logger_name,
        structlog.processors.TimeStamper(fmt="iso"),
        # Add context
        structlog.contextvars.merge_contextvars,
        # Format for JSON output in production, pretty print in debug
        structlog.dev.ConsoleRenderer() if settings.debug else structlog.processors.JSONRenderer(),
    ]
    
    # Configure structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    """Get a structured logger instance."""
    return structlog.get_logger(name)


def add_request_context(request_id: str, user_id: str | None = None) -> None:
    """Add request context to all log messages."""
    context: Dict[str, Any] = {"request_id": request_id}
    if user_id:
        context["user_id"] = user_id
    
    structlog.contextvars.clear_contextvars()
    structlog.contextvars.bind_contextvars(**context)
