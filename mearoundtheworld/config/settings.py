"""Application settings using Pydantic v1 for 12-factor configuration."""

from typing import List, Optional
from pydantic import BaseModel, Field, validator
import os
import json


class Settings(BaseModel):
    """Application settings loaded from environment variables."""

    # Application Settings
    app_name: str = Field(default="MeAroundTheWorld", description="Application name")
    app_version: str = Field(default="0.1.0", description="Application version")
    debug: bool = Field(default=False, description="Debug mode")
    log_level: str = Field(default="INFO", description="Logging level")

    # Server Settings
    host: str = Field(default="0.0.0.0", description="Server host")
    port: int = Field(default=8000, description="Server port")

    # JWT Settings
    jwt_secret_key: str = Field(..., description="JWT secret key")
    jwt_algorithm: str = Field(default="HS256", description="JWT algorithm")
    jwt_access_token_expire_minutes: int = Field(
        default=30, description="Access token expiration in minutes"
    )
    jwt_refresh_token_expire_days: int = Field(
        default=7, description="Refresh token expiration in days"
    )

    # Google OAuth Settings
    google_client_id: str = Field(..., description="Google OAuth client ID")
    google_client_secret: str = Field(..., description="Google OAuth client secret")
    google_redirect_uri: str = Field(..., description="Google OAuth redirect URI")

    # AWS Settings
    aws_region: str = Field(default="us-east-1", description="AWS region")
    aws_access_key_id: Optional[str] = Field(default=None, description="AWS access key ID")
    aws_secret_access_key: Optional[str] = Field(default=None, description="AWS secret access key")
    aws_endpoint_url: Optional[str] = Field(default=None, description="AWS endpoint URL (for LocalStack)")

    # DynamoDB Settings
    dynamodb_table_name: str = Field(
        default="mearoundtheworld", description="DynamoDB table name"
    )

    # S3 Settings
    s3_bucket_name: str = Field(
        default="mearoundtheworld-photos", description="S3 bucket name"
    )
    s3_presigned_url_expire_seconds: int = Field(
        default=20, description="S3 presigned URL expiration in seconds"
    )

    # CORS Settings
    cors_allowed_origins: List[str] = Field(
        default=["http://localhost:3000"], description="CORS allowed origins"
    )
    cors_allowed_methods: List[str] = Field(
        default=["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
        description="CORS allowed methods"
    )
    cors_allowed_headers: List[str] = Field(
        default=["*"], description="CORS allowed headers"
    )

    # Health Check Settings
    health_check_timeout: int = Field(
        default=5, description="Health check timeout in seconds"
    )

    @validator("log_level")
    def validate_log_level(cls, v: str) -> str:
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of {valid_levels}")
        return v.upper()

    @validator("jwt_algorithm")
    def validate_jwt_algorithm(cls, v: str) -> str:
        valid_algorithms = ["HS256", "HS384", "HS512", "RS256", "RS384", "RS512"]
        if v not in valid_algorithms:
            raise ValueError(f"JWT algorithm must be one of {valid_algorithms}")
        return v

    @validator("cors_allowed_origins", "cors_allowed_methods", "cors_allowed_headers", pre=True)
    def parse_list_fields(cls, v):
        if isinstance(v, str):
            v = v.strip()
            if v.startswith("[") and v.endswith("]"):
                return json.loads(v)
            return [item.strip() for item in v.split(",")]
        return v

    @classmethod
    def from_env(cls) -> "Settings":
        """Create settings instance from environment variables."""
        env = os.environ
        return cls(
            app_name=env.get("APP_NAME", cls.__fields__["app_name"].default),
            app_version=env.get("APP_VERSION", cls.__fields__["app_version"].default),
            debug=env.get("DEBUG", str(cls.__fields__["debug"].default)).lower() == "true",
            log_level=env.get("LOG_LEVEL", cls.__fields__["log_level"].default),
            host=env.get("HOST", cls.__fields__["host"].default),
            port=int(env.get("PORT", cls.__fields__["port"].default)),
            jwt_secret_key=env.get("JWT_SECRET_KEY", ""),
            jwt_algorithm=env.get("JWT_ALGORITHM", cls.__fields__["jwt_algorithm"].default),
            jwt_access_token_expire_minutes=int(
                env.get("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", cls.__fields__["jwt_access_token_expire_minutes"].default)
            ),
            jwt_refresh_token_expire_days=int(
                env.get("JWT_REFRESH_TOKEN_EXPIRE_DAYS", cls.__fields__["jwt_refresh_token_expire_days"].default)
            ),
            google_client_id=env.get("GOOGLE_CLIENT_ID", ""),
            google_client_secret=env.get("GOOGLE_CLIENT_SECRET", ""),
            google_redirect_uri=env.get("GOOGLE_REDIRECT_URI", ""),
            aws_region=env.get("AWS_REGION", cls.__fields__["aws_region"].default),
            aws_access_key_id=env.get("AWS_ACCESS_KEY_ID", cls.__fields__["aws_access_key_id"].default),
            aws_secret_access_key=env.get("AWS_SECRET_ACCESS_KEY", cls.__fields__["aws_secret_access_key"].default),
            aws_endpoint_url=env.get("AWS_ENDPOINT_URL", cls.__fields__["aws_endpoint_url"].default),
            dynamodb_table_name=env.get("DYNAMODB_TABLE_NAME", cls.__fields__["dynamodb_table_name"].default),
            s3_bucket_name=env.get("S3_BUCKET_NAME", cls.__fields__["s3_bucket_name"].default),
            s3_presigned_url_expire_seconds=int(
                env.get("S3_PRESIGNED_URL_EXPIRE_SECONDS", cls.__fields__["s3_presigned_url_expire_seconds"].default)
            ),
            cors_allowed_origins=env.get("CORS_ALLOWED_ORIGINS", cls.__fields__["cors_allowed_origins"].default),
            cors_allowed_methods=env.get("CORS_ALLOWED_METHODS", cls.__fields__["cors_allowed_methods"].default),
            cors_allowed_headers=env.get("CORS_ALLOWED_HEADERS", cls.__fields__["cors_allowed_headers"].default),
            health_check_timeout=int(env.get("HEALTH_CHECK_TIMEOUT", cls.__fields__["health_check_timeout"].default)),
        )
    
    @classmethod
    def from_debug(cls) -> "Settings":
        """Create settings instance from debug environment variables."""
        env = os.environ
        return cls(
            app_name=cls.__fields__["app_name"].default,
            app_version=cls.__fields__["app_version"].default,
            debug=True,
            log_level=cls.__fields__["log_level"].default,
            host=cls.__fields__["host"].default,
            port=int(cls.__fields__["port"].default),
            jwt_secret_key=env.get("JWT_SECRET_KEY", ""),
            jwt_algorithm=cls.__fields__["jwt_algorithm"].default,
            jwt_access_token_expire_minutes=int(
                cls.__fields__["jwt_access_token_expire_minutes"].default
            ),
            jwt_refresh_token_expire_days=int(
                cls.__fields__["jwt_refresh_token_expire_days"].default
            ),
            google_client_id=env.get("GOOGLE_CLIENT_ID", ""),
            google_client_secret=env.get("GOOGLE_CLIENT_SECRET", ""),
            google_redirect_uri="http://localhost:3000/auth/callback",
            aws_region="us-east-2",
            aws_access_key_id="test",
            aws_secret_access_key="test",
            aws_endpoint_url="http://localstack:4566",
            dynamodb_table_name=cls.__fields__["dynamodb_table_name"].default,
            s3_bucket_name=cls.__fields__["s3_bucket_name"].default,
            s3_presigned_url_expire_seconds=int(cls.__fields__["s3_presigned_url_expire_seconds"].default
            ),
            cors_allowed_origins=cls.__fields__["cors_allowed_origins"].default,
            cors_allowed_methods=cls.__fields__["cors_allowed_methods"].default,
            cors_allowed_headers=cls.__fields__["cors_allowed_headers"].default,
            health_check_timeout=int(cls.__fields__["health_check_timeout"].default),
        )


# Global settings instance
if os.environ.get("STAGE", "") == "Prod":
    settings = Settings.from_env()
else:
    print("Running in debug mode")
    settings = Settings.from_debug()


def get_settings() -> Settings:
    """Get the global settings instance."""
    return settings
