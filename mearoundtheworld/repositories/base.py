"""Base repository class for DynamoDB operations."""

import json
from typing import Any, Dict, List, Optional, Type, TypeVar

import boto3
from boto3.dynamodb.conditions import Key
from botocore.exceptions import ClientError

from mearoundtheworld.config.logging import get_logger
from mearoundtheworld.config.settings import settings
from mearoundtheworld.models.base import DynamoDBItem, PaginatedResponse, PaginationParams

logger = get_logger(__name__)

T = TypeVar("T", bound=DynamoDBItem)


class DynamoDBRepository:
    """Base repository for DynamoDB operations."""
    
    def __init__(self) -> None:
        """Initialize DynamoDB client and table."""
        if settings.debug:
            self._dynamodb = boto3.resource(
                "dynamodb",
                region_name=settings.aws_region,
                endpoint_url=settings.aws_endpoint_url,
                aws_access_key_id=settings.aws_access_key_id,
                aws_secret_access_key=settings.aws_secret_access_key,
            )
        else:
            self._dynamodb = boto3.resource(
                "dynamodb",
                region_name=settings.aws_region,
            )
        logger.error(
            "DynamoDB client initialized",
            region_name=settings.aws_region,
            endpoint_url=settings.aws_endpoint_url,
            access_key_id=settings.aws_access_key_id,
            secret_access_key=settings.aws_secret_access_key,
            table_name=settings.dynamodb_table_name,
        )
        self._table = self._dynamodb.Table(settings.dynamodb_table_name)
        logger.error("DynamoDB repository initialized", table_name=settings.dynamodb_table_name)
    
    async def create_item(self, item: T) -> T:
        """Create a new item in DynamoDB."""
        try:
            dynamodb_item = item.to_dynamodb_item()
            
            # Use condition expression to prevent overwriting existing items
            self._table.put_item(
                Item=dynamodb_item,
                ConditionExpression="attribute_not_exists(pk) AND attribute_not_exists(sk)"
            )
            
            logger.info("Item created", pk=item.pk, sk=item.sk)
            return item
            
        except ClientError as e:
            if e.response["Error"]["Code"] == "ConditionalCheckFailedException":
                logger.warning("Item already exists", pk=item.pk, sk=item.sk)
                raise ValueError(f"Item with pk={item.pk}, sk={item.sk} already exists")
            logger.error("Failed to create item", error=str(e), pk=item.pk, sk=item.sk)
            raise
    
    async def get_item(self, pk: str, sk: str, model_class: Type[T]) -> Optional[T]:
        """Get an item by partition and sort key."""
        try:
            response = self._table.get_item(Key={"pk": pk, "sk": sk})
            
            if "Item" not in response:
                logger.debug("Item not found", pk=pk, sk=sk)
                return None
            
            item = model_class.from_dynamodb_item(response["Item"])
            logger.debug("Item retrieved", pk=pk, sk=sk)
            return item
            
        except Exception as e:
            logger.error("Failed to get item", error=str(e), pk=pk, sk=sk)
            raise
    
    async def update_item(self, item: T) -> T:
        """Update an existing item in DynamoDB."""
        try:
            # Update the updated_at timestamp
            if hasattr(item, "updated_at"):
                from datetime import datetime
                item.updated_at = datetime.utcnow()
            
            dynamodb_item = item.to_dynamodb_item()
            
            # Use condition expression to ensure item exists
            self._table.put_item(
                Item=dynamodb_item,
                ConditionExpression="attribute_exists(pk) AND attribute_exists(sk)"
            )
            
            logger.info("Item updated", pk=item.pk, sk=item.sk)
            return item
            
        except ClientError as e:
            if e.response["Error"]["Code"] == "ConditionalCheckFailedException":
                logger.warning("Item not found for update", pk=item.pk, sk=item.sk)
                raise ValueError(f"Item with pk={item.pk}, sk={item.sk} not found")
            logger.error("Failed to update item", error=str(e), pk=item.pk, sk=item.sk)
            raise
    
    async def delete_item(self, pk: str, sk: str) -> bool:
        """Delete an item by partition and sort key."""
        try:
            response = self._table.delete_item(
                Key={"pk": pk, "sk": sk},
                ConditionExpression="attribute_exists(pk) AND attribute_exists(sk)",
                ReturnValues="ALL_OLD"
            )
            
            deleted = "Attributes" in response
            if deleted:
                logger.info("Item deleted", pk=pk, sk=sk)
            else:
                logger.warning("Item not found for deletion", pk=pk, sk=sk)
            
            return deleted
            
        except ClientError as e:
            if e.response["Error"]["Code"] == "ConditionalCheckFailedException":
                logger.warning("Item not found for deletion", pk=pk, sk=sk)
                return False
            logger.error("Failed to delete item", error=str(e), pk=pk, sk=sk)
            raise
    
    async def query_items(
        self,
        pk: str,
        sk_prefix: Optional[str] = None,
        model_class: Type[T] = None,
        pagination: Optional[PaginationParams] = None,
        scan_index_forward: bool = True,
    ) -> PaginatedResponse:
        """Query items by partition key with optional sort key prefix."""
        try:
            # Build key condition
            key_condition = Key("pk").eq(pk)
            if sk_prefix:
                key_condition = key_condition & Key("sk").begins_with(sk_prefix)
            
            # Build query parameters
            query_params = {
                "KeyConditionExpression": key_condition,
                "ScanIndexForward": scan_index_forward,
            }
            
            if pagination:
                query_params["Limit"] = pagination.limit
                if pagination.last_evaluated_key:
                    try:
                        query_params["ExclusiveStartKey"] = json.loads(pagination.last_evaluated_key)
                    except json.JSONDecodeError:
                        logger.warning("Invalid last_evaluated_key format", key=pagination.last_evaluated_key)
            
            response = self._table.query(**query_params)
            
            # Convert items to model instances
            items = []
            if model_class:
                items = [model_class.from_dynamodb_item(item) for item in response.get("Items", [])]
            else:
                items = response.get("Items", [])
            
            # Handle pagination
            last_evaluated_key = None
            if "LastEvaluatedKey" in response:
                last_evaluated_key = json.dumps(response["LastEvaluatedKey"])
            
            result = PaginatedResponse(
                items=items,
                last_evaluated_key=last_evaluated_key,
                has_more=last_evaluated_key is not None,
            )
            
            logger.debug("Query completed", pk=pk, sk_prefix=sk_prefix, count=len(items))
            return result
            
        except Exception as e:
            logger.error("Failed to query items", error=str(e), pk=pk, sk_prefix=sk_prefix)
            raise
