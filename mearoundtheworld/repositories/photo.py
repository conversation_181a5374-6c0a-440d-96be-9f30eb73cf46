"""Photo repository for DynamoDB operations."""

from typing import Optional
from uuid import UUID, uuid4

from mearoundtheworld.config.logging import get_logger
from mearoundtheworld.models.base import PaginatedResponse, PaginationParams
from mearoundtheworld.models.photo import Photo, PhotoCreate

from .base import DynamoDBRepository

logger = get_logger(__name__)


class PhotoRepository(DynamoDBRepository):
    """Repository for photo operations."""
    
    async def create_photo(self, user_id: UUID, photo_data: PhotoCreate) -> Photo:
        """Create a new photo for a user."""
        photo_id = uuid4()
        photo = Photo(
            pk=Photo.create_pk(user_id),
            sk=Photo.create_sk(photo_id),
            user_id=user_id,
            photo_id=photo_id,
            place_name=photo_data.place_name,
            object_key=photo_data.object_key,
            latitude=photo_data.latitude,
            longitude=photo_data.longitude,
            brief_description=photo_data.brief_description,
            trip_date=photo_data.trip_date,
        )
        
        return await self.create_item(photo)
    
    async def get_photo_by_id(self, user_id: UUID, photo_id: UUID) -> Optional[Photo]:
        """Get photo by ID for a specific user."""
        pk = Photo.create_pk(user_id)
        sk = Photo.create_sk(photo_id)
        return await self.get_item(pk, sk, Photo)
    
    async def get_user_photos(
        self, 
        user_id: UUID, 
        pagination: Optional[PaginationParams] = None
    ) -> PaginatedResponse:
        """Get all photos for a user with pagination."""
        pk = Photo.create_pk(user_id)
        sk_prefix = "PHOTO#"
        
        return await self.query_items(
            pk=pk,
            sk_prefix=sk_prefix,
            model_class=Photo,
            pagination=pagination,
            scan_index_forward=False,  # Most recent first
        )
    
    async def update_photo(self, photo: Photo) -> Photo:
        """Update photo information."""
        return await self.update_item(photo)
    
    async def delete_photo(self, user_id: UUID, photo_id: UUID) -> bool:
        """Delete photo by ID for a specific user."""
        pk = Photo.create_pk(user_id)
        sk = Photo.create_sk(photo_id)
        return await self.delete_item(pk, sk)
    
    async def photo_exists(self, user_id: UUID, photo_id: UUID) -> bool:
        """Check if photo exists for a user."""
        photo = await self.get_photo_by_id(user_id, photo_id)
        return photo is not None
    
    async def get_photo_count_for_user(self, user_id: UUID) -> int:
        """Get total count of photos for a user."""
        try:
            pk = Photo.create_pk(user_id)
            sk_prefix = "PHOTO#"
            
            # Use query with Select='COUNT' for efficient counting
            from boto3.dynamodb.conditions import Key
            
            response = self._table.query(
                KeyConditionExpression=Key("pk").eq(pk) & Key("sk").begins_with(sk_prefix),
                Select="COUNT"
            )
            
            count = response.get("Count", 0)
            logger.debug("Photo count retrieved", user_id=user_id, count=count)
            return count
            
        except Exception as e:
            logger.error("Failed to get photo count", error=str(e), user_id=user_id)
            raise
