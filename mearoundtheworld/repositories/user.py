"""User repository for DynamoDB operations."""

from typing import Optional
from uuid import UUID, uuid4

from boto3.dynamodb.conditions import Attr

from mearoundtheworld.config.logging import get_logger
from mearoundtheworld.models.user import User, UserCreate

from .base import DynamoDBRepository

logger = get_logger(__name__)


class UserRepository(DynamoDBRepository):
    """Repository for user operations."""
    
    async def create_user(self, user_data: UserCreate) -> User:
        """Create a new user."""
        user_id = uuid4()
        user = User(
            id=user_id,
            pk=f"USER#{user_id}",
            email=user_data.email,
            name=user_data.name,
            picture_url=user_data.picture_url,
            google_sub=user_data.google_sub,
        )

        return await self.create_item(user)
    
    async def get_user_by_id(self, user_id: UUID) -> Optional[User]:
        """Get user by ID."""
        pk = User.create_pk(user_id)
        return await self.get_item(pk, "PROFILE", User)
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email using scan operation."""
        try:
            response = self._table.scan(
                FilterExpression=Attr("email").eq(email) & Attr("sk").eq("PROFILE")
            )
            
            items = response.get("Items", [])
            if not items:
                logger.debug("User not found by email", email=email)
                return None
            
            user = User.from_dynamodb_item(items[0])
            logger.debug("User found by email", email=email, user_id=user.id)
            return user
            
        except Exception as e:
            logger.error("Failed to get user by email", error=str(e), email=email)
            raise
    
    async def get_user_by_google_sub(self, google_sub: str) -> Optional[User]:
        """Get user by Google subject ID using scan operation."""
        try:
            response = self._table.scan(
                FilterExpression=Attr("google_sub").eq(google_sub) & Attr("sk").eq("PROFILE")
            )
            
            items = response.get("Items", [])
            if not items:
                logger.debug("User not found by google_sub", google_sub=google_sub)
                return None
            
            user = User.from_dynamodb_item(items[0])
            logger.debug("User found by google_sub", google_sub=google_sub, user_id=user.id)
            return user
            
        except Exception as e:
            logger.error("Failed to get user by google_sub", error=str(e), google_sub=google_sub)
            raise
    
    async def update_user(self, user: User) -> User:
        """Update user information."""
        return await self.update_item(user)
    
    async def update_user_login(self, user_id: UUID) -> Optional[User]:
        """Update user's last login timestamp."""
        user = await self.get_user_by_id(user_id)
        if not user:
            return None
        
        user.update_last_login()
        return await self.update_user(user)
    
    async def delete_user(self, user_id: UUID) -> bool:
        """Delete user by ID."""
        pk = User.create_pk(user_id)
        return await self.delete_item(pk, "PROFILE")
    
    async def user_exists_by_email(self, email: str) -> bool:
        """Check if user exists by email."""
        user = await self.get_user_by_email(email)
        return user is not None
    
    async def user_exists_by_google_sub(self, google_sub: str) -> bool:
        """Check if user exists by Google subject ID."""
        user = await self.get_user_by_google_sub(google_sub)
        return user is not None
