"""Shared utilities for AWS Lambda functions."""

import json
import os
import asyncio
from typing import Any, Dict, Optional, Callable
from datetime import datetime
import structlog
from functools import wraps

# Configure structured logging for Lambda
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


def get_env_var(name: str, default: Optional[str] = None) -> str:
    """Get environment variable with optional default."""
    value = os.getenv(name, default)
    if value is None:
        raise ValueError(f"Environment variable {name} is required")
    return value


def create_response(
    status_code: int,
    body: Any,
    headers: Optional[Dict[str, str]] = None
) -> Dict[str, Any]:
    """Create standardized Lambda response."""
    default_headers = {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type,Authorization",
        "Access-Control-Allow-Methods": "GET,POST,PUT,DELETE,OPTIONS,PATCH"
    }
    
    if headers:
        default_headers.update(headers)
    
    response = {
        "statusCode": status_code,
        "headers": default_headers,
        "body": json.dumps(body, default=str) if not isinstance(body, str) else body
    }

    logger.error("Creating response", response=response)
    return response


def create_error_response(
    status_code: int,
    message: str,
    details: Optional[str] = None
) -> Dict[str, Any]:
    """Create standardized error response."""
    error_body = {
        "error": message,
        "timestamp": datetime.utcnow().isoformat(),
    }
    
    if details:
        error_body["details"] = details
    
    return create_response(status_code, error_body)


def extract_user_id_from_token(event: Dict[str, Any]) -> Optional[str]:
    """Extract user ID from JWT token in Authorization header."""
    try:
        headers = event.get("headers", {})
        auth_header = headers.get("Authorization") or headers.get("authorization")
        
        if not auth_header or not auth_header.startswith("Bearer "):
            return None
        
        token = auth_header.replace("Bearer ", "")
        
        # Import here to avoid circular imports
        from mearoundtheworld.services.auth import auth_service
        
        token_data = auth_service.verify_token(token, "access")
        if token_data:
            return str(token_data.user_id)
        
        return None
        
    except Exception as e:
        logger.error("Failed to extract user ID from token", error=str(e))
        return None


def get_path_parameter(event: Dict[str, Any], param_name: str) -> Optional[str]:
    """Extract path parameter from Lambda event."""
    path_params = event.get("pathParameters") or {}
    return path_params.get(param_name)


def get_query_parameter(event: Dict[str, Any], param_name: str, default: Optional[str] = None) -> Optional[str]:
    """Extract query parameter from Lambda event."""
    query_params = event.get("queryStringParameters") or {}
    return query_params.get(param_name, default)


def parse_json_body(event: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Parse JSON body from Lambda event."""
    try:
        body = event.get("body")
        if not body:
            return None
        
        if isinstance(body, str):
            return json.loads(body)
        
        return body
        
    except json.JSONDecodeError as e:
        logger.error("Failed to parse JSON body", error=str(e))
        return None


def require_authentication(event: Dict[str, Any]) -> str:
    """Require authentication and return user ID."""
    user_id = extract_user_id_from_token(event)
    if not user_id:
        raise ValueError("Authentication required")
    return user_id


def handle_cors_preflight(event: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Handle CORS preflight requests."""
    if event.get("httpMethod") == "OPTIONS":
        return create_response(200, "", {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Content-Type,Authorization",
            "Access-Control-Allow-Methods": "GET,POST,PUT,DELETE,OPTIONS,PATCH"
        })
    return None


def async_handler(func: Callable) -> Callable:
    """Decorator to handle async Lambda functions."""
    @wraps(func)
    def wrapper(event, context):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(func(event, context))
        finally:
            loop.close()
    return wrapper
