version = 0.1

[default.deploy.parameters]
stack_name = "mearoundtheworld"
resolve_s3 = true
s3_prefix = "mearoundtheworld"
region = "us-east-2"
confirm_changeset = true
capabilities = "CAPABILITY_IAM"
parameter_overrides = "Stage=\"Prod\" GoogleClientId=\"875949499999-fijelpv58a0kiibs6il501ppe6n2fj2f.apps.googleusercontent.com\" GoogleRedirectUri=\"https://www.mearoundtheworld.com.br/auth/callback\" ApiCertificateArn=\"arn:aws:acm:us-east-2:574645932734:certificate/ba302158-2931-4b74-9754-41afc1cd511c\""
image_repositories = []
