"""Lambda function for POST /photos endpoint."""

import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from shared.lambda_utils import (
    create_response,
    create_error_response,
    require_authentication,
    parse_json_body,
    handle_cors_preflight,
    async_handler,
    logger
)


@async_handler
async def lambda_handler(event, context):
    """
    AWS Lambda handler for POST /photos endpoint.
    
    Create a new photo record.
    """
    try:
        # Handle CORS preflight
        cors_response = handle_cors_preflight(event)
        if cors_response:
            return cors_response
        
        # Only allow POST method
        if event.get("httpMethod") != "POST":
            return create_error_response(405, "Method not allowed")
        
        # Require authentication
        try:
            user_id = require_authentication(event)
        except ValueError:
            return create_error_response(401, "Authentication required")
        
        # Parse request body
        body = parse_json_body(event)
        if not body:
            return create_error_response(400, "Invalid JSON body")
        
        # Import here to avoid cold start issues
        from mearoundtheworld.repositories.photo import PhotoRepository
        from mearoundtheworld.models.photo import PhotoCreate, PhotoResponse
        from mearoundtheworld.services.s3 import s3_service
        from uuid import UUID
        from pydantic import ValidationError
        
        # Validate and create photo data
        try:
            photo_data = PhotoCreate(**body)
        except ValidationError as e:
            return create_error_response(400, f"Invalid photo data: {str(e)}")
        
        # Create photo record
        photo_repository = PhotoRepository()
        photo = await photo_repository.create_photo(UUID(user_id), photo_data)
        
        # Generate download URL
        download_url = s3_service.generate_presigned_download_url(photo.object_key)
        
        logger.info("Photo created", user_id=user_id, photo_id=photo.photo_id)
        
        response_data = PhotoResponse.from_photo(photo, download_url).dict()
        response_data["s3_url"] = response_data["s3_url"].replace("localstack", "localhost")

        logger.error("Photo created", response_data=response_data)

        return create_response(200, response_data)
        
    except Exception as e:
        logger.error("Failed to create photo", error=str(e))
        return create_error_response(500, "Failed to create photo")


# For local testing
if __name__ == "__main__":
    test_event = {
        "httpMethod": "POST",
        "headers": {
            "Authorization": "Bearer test-token"
        },
        "body": '{"object_key": "test-key", "place_name": "Test Place", "latitude": 40.7128, "longitude": -74.0060}'
    }
    
    result = lambda_handler(test_event, None)
    print(result)
