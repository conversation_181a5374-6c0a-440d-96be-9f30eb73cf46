"""Lambda function for GET /photos endpoint."""

import asyncio
import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from shared.lambda_utils import (
    create_response,
    create_error_response,
    require_authentication,
    get_query_parameter,
    handle_cors_preflight,
    async_handler,
    logger
)


@async_handler
async def lambda_handler(event, context):
    """
    AWS Lambda handler for GET /photos endpoint.
    
    List user's photos with pagination.
    """
    try:
        # Handle CORS preflight
        cors_response = handle_cors_preflight(event)
        if cors_response:
            return cors_response
        
        # Only allow GET method
        if event.get("httpMethod") != "GET":
            return create_error_response(405, "Method not allowed")
        
        # Require authentication
        try:
            user_id = require_authentication(event)
        except ValueError:
            return create_error_response(401, "Authentication required")
        
        # Get query parameters
        limit_str = get_query_parameter(event, "limit", "20")
        limit = int(limit_str) if limit_str else 20
        last_evaluated_key = get_query_parameter(event, "last_evaluated_key")
        
        # Validate limit
        if limit > 100:
            limit = 100
        elif limit < 1:
            limit = 20
        
        # Import here to avoid cold start issues
        from mearoundtheworld.repositories.photo import PhotoRepository
        from mearoundtheworld.models.photo import PhotoResponse
        from mearoundtheworld.models.base import PaginatedResponse, PaginationParams
        from mearoundtheworld.services.s3 import s3_service
        from uuid import UUID
        
        # Get photos
        pagination = PaginationParams(limit=limit, last_evaluated_key=last_evaluated_key)
        photo_repository = PhotoRepository()
        result = await photo_repository.get_user_photos(UUID(user_id), pagination)
        
        # Generate download URLs for photos
        photos_with_urls = []
        for photo in result.items:
            download_url = s3_service.generate_presigned_download_url(photo.object_key)
            photos_with_urls.append(PhotoResponse.from_photo(photo, download_url))
        
        logger.debug("Photos listed", user_id=user_id, count=len(photos_with_urls))
        
        response_data = PaginatedResponse(
            items=[photo.dict() for photo in photos_with_urls],
            last_evaluated_key=result.last_evaluated_key,
            has_more=result.has_more,
            total_count=result.total_count
        ).dict()
        
        return create_response(200, response_data)
        
    except Exception as e:
        logger.error("Failed to list photos", error=str(e))
        return create_error_response(500, "Failed to list photos")


# For local testing
if __name__ == "__main__":
    test_event = {
        "httpMethod": "GET",
        "headers": {
            "Authorization": "Bearer test-token"
        },
        "queryStringParameters": {
            "limit": "10"
        }
    }
    
    result = asyncio.run(lambda_handler(test_event, None))
    print(result)
