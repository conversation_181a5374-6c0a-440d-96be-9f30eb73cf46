"""Lambda function for GET /photos/u/{user_id} endpoint."""

import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from shared.lambda_utils import (
    create_response,
    create_error_response,
    require_authentication,
    get_path_parameter,
    get_query_parameter,
    handle_cors_preflight,
    async_handler,
    logger
)


@async_handler
async def lambda_handler(event, context):
    """
    AWS Lambda handler for GET /photos/u/{user_id} endpoint.
    
    List photos for a specific user.
    """
    try:
        # Handle CORS preflight
        cors_response = handle_cors_preflight(event)
        if cors_response:
            return cors_response
        
        # Only allow GET method
        if event.get("httpMethod") != "GET":
            return create_error_response(405, "Method not allowed")
        
        # Require authentication (even for viewing other users' photos)
        try:
            require_authentication(event)
        except ValueError:
            return create_error_response(401, "Authentication required")
        
        # Get path parameter
        user_id = get_path_parameter(event, "user_id")
        if not user_id:
            return create_error_response(400, "User ID is required")
        
        # Get query parameters
        limit = int(get_query_parameter(event, "limit", "20"))
        last_evaluated_key = get_query_parameter(event, "last_evaluated_key")
        
        # Validate limit
        if limit > 100:
            limit = 100
        elif limit < 1:
            limit = 20
        
        # Import here to avoid cold start issues
        from mearoundtheworld.repositories.photo import PhotoRepository
        from mearoundtheworld.models.photo import PhotoResponse
        from mearoundtheworld.models.base import PaginatedResponse, PaginationParams
        from mearoundtheworld.services.s3 import s3_service
        from uuid import UUID
        
        # Get photos for the specified user
        pagination = PaginationParams(limit=limit, last_evaluated_key=last_evaluated_key)
        photo_repository = PhotoRepository()
        result = await photo_repository.get_user_photos(UUID(user_id), pagination)
        
        # Generate download URLs for photos
        photos_with_urls = []
        for photo in result.items:
            download_url = s3_service.generate_presigned_download_url(photo.object_key)
            photos_with_urls.append(PhotoResponse.from_photo(photo, download_url))
        
        logger.debug("User photos retrieved", target_user_id=user_id, count=len(photos_with_urls))
        
        response_data = PaginatedResponse(
            items=[photo.dict() for photo in photos_with_urls],
            last_evaluated_key=result.last_evaluated_key,
            has_more=result.has_more,
            total_count=result.total_count
        ).dict()
        
        return create_response(200, response_data)
        
    except Exception as e:
        logger.error("Failed to list user photos", error=str(e))
        return create_error_response(500, "Failed to list user photos")


# For local testing
if __name__ == "__main__":
    test_event = {
        "httpMethod": "GET",
        "headers": {
            "Authorization": "Bearer test-token"
        },
        "pathParameters": {
            "user_id": "12345678-1234-1234-1234-123456789012"
        },
        "queryStringParameters": {
            "limit": "10"
        }
    }
    
    result = lambda_handler(test_event, None)
    print(result)
