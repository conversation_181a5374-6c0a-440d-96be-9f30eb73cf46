"""Lambda function for GET /photos/{photo_id} endpoint."""

import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from shared.lambda_utils import (
    create_response,
    create_error_response,
    require_authentication,
    get_path_parameter,
    handle_cors_preflight,
    async_handler,
    logger
)


@async_handler
async def lambda_handler(event, context):
    """
    AWS Lambda handler for GET /photos/{photo_id} endpoint.
    
    Get photo details by ID.
    """
    try:
        # Handle CORS preflight
        cors_response = handle_cors_preflight(event)
        if cors_response:
            return cors_response
        
        # Only allow GET method
        if event.get("httpMethod") != "GET":
            return create_error_response(405, "Method not allowed")
        
        # Require authentication
        try:
            user_id = require_authentication(event)
        except ValueError:
            return create_error_response(401, "Authentication required")
        
        # Get path parameter
        photo_id = get_path_parameter(event, "photo_id")
        if not photo_id:
            return create_error_response(400, "Photo ID is required")
        
        # Import here to avoid cold start issues
        from mearoundtheworld.repositories.photo import PhotoRepository
        from mearoundtheworld.models.photo import PhotoResponse
        from mearoundtheworld.services.s3 import s3_service
        from uuid import UUID
        
        # Get photo
        photo_repository = PhotoRepository()
        photo = await photo_repository.get_photo_by_id(UUID(photo_id), UUID(user_id))
        
        if not photo:
            return create_error_response(404, "Photo not found")
        
        # Check if user owns the photo or if it's public
        # For now, we'll allow any authenticated user to view any photo
        # In production, you might want to add privacy controls
        
        # Generate download URL
        download_url = s3_service.generate_presigned_download_url(photo.object_key)
        
        logger.debug("Photo retrieved", photo_id=photo_id, user_id=user_id)
        
        response_data = PhotoResponse.from_photo(photo, download_url).dict()
        return create_response(200, response_data)
        
    except Exception as e:
        logger.error("Failed to get photo", error=str(e))
        return create_error_response(500, "Failed to get photo")


# For local testing
if __name__ == "__main__":
    test_event = {
        "httpMethod": "GET",
        "headers": {
            "Authorization": "Bearer test-token"
        },
        "pathParameters": {
            "photo_id": "12345678-1234-1234-1234-123456789012"
        }
    }
    
    result = lambda_handler(test_event, None)
    print(result)
