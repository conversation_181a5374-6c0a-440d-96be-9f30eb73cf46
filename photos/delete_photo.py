"""Lambda function for DELETE /photos/{photo_id} endpoint."""

import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from shared.lambda_utils import (
    create_response,
    create_error_response,
    require_authentication,
    get_path_parameter,
    handle_cors_preflight,
    async_handler,
    logger
)


@async_handler
async def lambda_handler(event, context):
    """
    AWS Lambda handler for DELETE /photos/{photo_id} endpoint.
    
    Delete photo and its S3 object.
    """
    try:
        # Handle CORS preflight
        cors_response = handle_cors_preflight(event)
        if cors_response:
            return cors_response
        
        # Only allow DELETE method
        if event.get("httpMethod") != "DELETE":
            return create_error_response(405, "Method not allowed")
        
        # Require authentication
        try:
            user_id = require_authentication(event)
        except ValueError:
            return create_error_response(401, "Authentication required")
        
        # Get path parameter
        photo_id = get_path_parameter(event, "photo_id")
        if not photo_id:
            return create_error_response(400, "Photo ID is required")
        
        # Import here to avoid cold start issues
        from mearoundtheworld.repositories.photo import PhotoRepository
        from mearoundtheworld.services.s3 import s3_service
        from uuid import UUID
        
        # Get photo to verify ownership
        photo_repository = PhotoRepository()
        photo = await photo_repository.get_photo_by_id(user_id=UUID(user_id), photo_id=UUID(photo_id))
        
        if not photo:
            return create_error_response(404, "Photo not found")
        
        # Check if user owns the photo
        if str(photo.user_id) != user_id:
            return create_error_response(403, "You can only delete your own photos")
        
        # Delete from S3 first
        try:
            await s3_service.delete_object(photo.object_key)
            logger.info("Photo deleted from S3", object_key=photo.object_key)
        except Exception as e:
            logger.warning("Failed to delete from S3", object_key=photo.object_key, error=str(e))
            # Continue with database deletion even if S3 deletion fails
        
        # Delete from database
        success = await photo_repository.delete_photo(UUID(user_id), UUID(photo_id))
        
        if not success:
            return create_error_response(404, "Photo not found")
        
        logger.info("Photo deleted", photo_id=photo_id, user_id=user_id)
        
        return create_response(204, "")  # No content response
        
    except Exception as e:
        logger.error("Failed to delete photo", error=str(e))
        return create_error_response(500, "Failed to delete photo")


# For local testing
if __name__ == "__main__":
    test_event = {
        "httpMethod": "DELETE",
        "headers": {
            "Authorization": "Bearer test-token"
        },
        "pathParameters": {
            "photo_id": "12345678-1234-1234-1234-123456789012"
        }
    }
    
    result = lambda_handler(test_event, None)
    print(result)
