"""Lambda function for PATCH /photos/{photo_id} endpoint."""

import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from shared.lambda_utils import (
    create_response,
    create_error_response,
    require_authentication,
    get_path_parameter,
    parse_json_body,
    handle_cors_preflight,
    async_handler,
    logger
)


@async_handler
async def lambda_handler(event, context):
    """
    AWS Lambda handler for PATCH /photos/{photo_id} endpoint.
    
    Update photo metadata.
    """
    try:
        # Handle CORS preflight
        cors_response = handle_cors_preflight(event)
        if cors_response:
            return cors_response
        
        # Only allow PATCH method
        if event.get("httpMethod") != "PATCH":
            return create_error_response(405, "Method not allowed")
        
        # Require authentication
        try:
            user_id = require_authentication(event)
        except ValueError:
            return create_error_response(401, "Authentication required")
        
        # Get path parameter
        photo_id = get_path_parameter(event, "photo_id")
        if not photo_id:
            return create_error_response(400, "Photo ID is required")
        
        # Parse request body
        body = parse_json_body(event)
        if not body:
            return create_error_response(400, "Invalid JSON body")
        
        # Import here to avoid cold start issues
        from mearoundtheworld.repositories.photo import PhotoRepository
        from mearoundtheworld.models.photo import PhotoUpdate, PhotoResponse
        from mearoundtheworld.services.s3 import s3_service
        from uuid import UUID
        from pydantic import ValidationError
        
        # Get photo to verify ownership
        photo_repository = PhotoRepository()
        photo = await photo_repository.get_photo_by_id(UUID(user_id), UUID(photo_id))
        
        if not photo:
            return create_error_response(404, "Photo not found")
        
        # Check if user owns the photo
        if str(photo.user_id) != user_id:
            return create_error_response(403, "You can only update your own photos")
        
        # Validate update data
        try:
            update_data = PhotoUpdate(**body)
        except ValidationError as e:
            return create_error_response(400, f"Invalid update data: {str(e)}")
        
        # Update photo
        updated_photo = await photo_repository.update_photo(UUID(photo_id), update_data)
        
        if not updated_photo:
            return create_error_response(404, "Photo not found")
        
        # Generate download URL
        download_url = s3_service.generate_presigned_download_url(updated_photo.object_key)
        
        logger.info("Photo updated", photo_id=photo_id, user_id=user_id)
        
        response_data = PhotoResponse.from_photo(updated_photo, download_url).dict()
        return create_response(200, response_data)
        
    except Exception as e:
        logger.error("Failed to update photo", error=str(e))
        return create_error_response(500, "Failed to update photo")


# For local testing
if __name__ == "__main__":
    test_event = {
        "httpMethod": "PATCH",
        "headers": {
            "Authorization": "Bearer test-token"
        },
        "pathParameters": {
            "photo_id": "12345678-1234-1234-1234-123456789012"
        },
        "body": '{"place_name": "Updated Place", "brief_description": "Updated description"}'
    }
    
    result = lambda_handler(test_event, None)
    print(result)
