"""Lambda function for POST /photos/upload-url endpoint."""

import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from shared.lambda_utils import (
    create_response,
    create_error_response,
    require_authentication,
    parse_json_body,
    handle_cors_preflight,
    logger
)


def lambda_handler(event, context):
    """
    AWS Lambda handler for POST /photos/upload-url endpoint.
    
    Get pre-signed URL for photo upload.
    """
    try:
        # Handle CORS preflight
        cors_response = handle_cors_preflight(event)
        if cors_response:
            return cors_response
        
        # Only allow POST method
        if event.get("httpMethod") != "POST":
            return create_error_response(405, "Method not allowed")
        
        # Require authentication
        try:
            user_id = require_authentication(event)
        except ValueError:
            return create_error_response(401, "Authentication required")
        
        # Parse request body
        body = parse_json_body(event)
        if not body:
            return create_error_response(400, "Invalid JSON body")
        
        filename = body.get("filename")
        content_type = body.get("content_type")
        
        if not filename:
            return create_error_response(400, "Filename is required")
        
        # Import here to avoid cold start issues
        from mearoundtheworld.services.s3 import s3_service
        import uuid
        
        # Generate unique object key
        file_extension = filename.split('.')[-1] if '.' in filename else ''
        object_key = f"{user_id}/{uuid.uuid4()}"
        if file_extension:
            object_key += f".{file_extension}"
        
        # Generate pre-signed upload URL
        upload_url, fields = s3_service.generate_presigned_upload_url(
            object_key=object_key,
            content_type=content_type
        )
        
        logger.info("Upload URL generated", user_id=user_id, object_key=object_key)
        
        response_data = {
            "upload_url": upload_url,
            "object_key": object_key,
            "fields": fields,
            "expires_in": 20
        }
        
        return create_response(200, response_data)
        
    except Exception as e:
        logger.error("Failed to generate upload URL", error=str(e))
        return create_error_response(500, "Failed to generate upload URL")


# For local testing
if __name__ == "__main__":
    test_event = {
        "httpMethod": "POST",
        "headers": {
            "Authorization": "Bearer test-token"
        },
        "body": '{"filename": "test.jpg", "content_type": "image/jpeg"}'
    }
    
    result = lambda_handler(test_event, None)
    print(result)
