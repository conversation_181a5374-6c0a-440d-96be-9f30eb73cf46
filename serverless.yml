service: mearoundtheworld-api

frameworkVersion: '3'

provider:
  name: aws
  runtime: python3.11
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-1'}
  timeout: 30
  memorySize: 256
  
  environment:
    STAGE: ${self:provider.stage}
    REGION: ${self:provider.region}
    DYNAMODB_TABLE_NAME: ${self:custom.tableName}
    S3_BUCKET_NAME: ${self:custom.bucketName}
    JWT_SECRET_KEY: ${env:JWT_SECRET_KEY}
    JWT_ALGORITHM: ${env:JWT_ALGORITHM, 'HS256'}
    GOOGLE_CLIENT_ID: ${env:GOOGLE_CLIENT_ID}
    GOOGLE_CLIENT_SECRET: ${env:GOOGLE_CLIENT_SECRET}
    GOOGLE_REDIRECT_URI: ${env:GOOGLE_REDIRECT_URI}
    DEBUG: ${env:DEBUG, 'false'}
    APP_VERSION: ${env:APP_VERSION, '1.0.0'}
  
  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - dynamodb:Query
            - dynamodb:Scan
            - dynamodb:GetItem
            - dynamodb:PutItem
            - dynamodb:UpdateItem
            - dynamodb:DeleteItem
          Resource:
            - "arn:aws:dynamodb:${self:provider.region}:*:table/${self:custom.tableName}"
            - "arn:aws:dynamodb:${self:provider.region}:*:table/${self:custom.tableName}/index/*"
        - Effect: Allow
          Action:
            - s3:GetObject
            - s3:PutObject
            - s3:DeleteObject
            - s3:GetObjectVersion
          Resource:
            - "arn:aws:s3:::${self:custom.bucketName}/*"
        - Effect: Allow
          Action:
            - s3:ListBucket
            - s3:HeadBucket
          Resource:
            - "arn:aws:s3:::${self:custom.bucketName}"

custom:
  tableName: mearoundtheworld-${self:provider.stage}
  bucketName: mearoundtheworld-photos-${self:provider.stage}
  
  pythonRequirements:
    dockerizePip: true
    slim: true
    strip: false

plugins:
  - serverless-python-requirements

functions:
  # Authentication endpoints
  getLoginUrl:
    handler: auth/get_login_url.lambda_handler
    events:
      - http:
          path: auth/login
          method: get
          cors: true
  
  login:
    handler: auth/login.lambda_handler
    events:
      - http:
          path: auth/login
          method: post
          cors: true
  
  refreshToken:
    handler: auth/refresh_token.lambda_handler
    events:
      - http:
          path: auth/refresh
          method: post
          cors: true
  
  # User endpoints
  getCurrentUser:
    handler: users/get_current_user.lambda_handler
    events:
      - http:
          path: users/me
          method: get
          cors: true
  
  # Photo endpoints
  getUploadUrl:
    handler: photos/get_upload_url.lambda_handler
    events:
      - http:
          path: photos/upload-url
          method: post
          cors: true
  
  createPhoto:
    handler: photos/create_photo.lambda_handler
    events:
      - http:
          path: photos
          method: post
          cors: true
  
  listPhotos:
    handler: photos/list_photos.lambda_handler
    events:
      - http:
          path: photos
          method: get
          cors: true
  
  listUserPhotos:
    handler: photos/list_user_photos.lambda_handler
    events:
      - http:
          path: photos/u/{user_id}
          method: get
          cors: true
  
  getPhoto:
    handler: photos/get_photo.lambda_handler
    events:
      - http:
          path: photos/{photo_id}
          method: get
          cors: true
  
  updatePhoto:
    handler: photos/update_photo.lambda_handler
    events:
      - http:
          path: photos/{photo_id}
          method: patch
          cors: true
  
  deletePhoto:
    handler: photos/delete_photo.lambda_handler
    events:
      - http:
          path: photos/{photo_id}
          method: delete
          cors: true
  
  # Health endpoints
  healthCheck:
    handler: health/health_check.lambda_handler
    events:
      - http:
          path: health
          method: get
          cors: true
  
  detailedHealth:
    handler: health/detailed_health.lambda_handler
    events:
      - http:
          path: health/detailed
          method: get
          cors: true

resources:
  Resources:
    # DynamoDB Table
    DynamoDBTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:custom.tableName}
        BillingMode: PAY_PER_REQUEST
        AttributeDefinitions:
          - AttributeName: pk
            AttributeType: S
          - AttributeName: sk
            AttributeType: S
          - AttributeName: gsi1pk
            AttributeType: S
          - AttributeName: gsi1sk
            AttributeType: S
        KeySchema:
          - AttributeName: pk
            KeyType: HASH
          - AttributeName: sk
            KeyType: RANGE
        GlobalSecondaryIndexes:
          - IndexName: GSI1
            KeySchema:
              - AttributeName: gsi1pk
                KeyType: HASH
              - AttributeName: gsi1sk
                KeyType: RANGE
            Projection:
              ProjectionType: ALL
        StreamSpecification:
          StreamViewType: NEW_AND_OLD_IMAGES
        PointInTimeRecoverySpecification:
          PointInTimeRecoveryEnabled: true
        Tags:
          - Key: Environment
            Value: ${self:provider.stage}
          - Key: Service
            Value: ${self:service}
    
    # S3 Bucket for photos
    S3Bucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: ${self:custom.bucketName}
        CorsConfiguration:
          CorsRules:
            - AllowedHeaders:
                - "*"
              AllowedMethods:
                - GET
                - PUT
                - POST
                - DELETE
                - HEAD
              AllowedOrigins:
                - "*"
              MaxAge: 3000
        PublicAccessBlockConfiguration:
          BlockPublicAcls: true
          BlockPublicPolicy: true
          IgnorePublicAcls: true
          RestrictPublicBuckets: true
        VersioningConfiguration:
          Status: Enabled
        Tags:
          - Key: Environment
            Value: ${self:provider.stage}
          - Key: Service
            Value: ${self:service}

  Outputs:
    ApiGatewayRestApiId:
      Value:
        Ref: ApiGatewayRestApi
      Export:
        Name: ${self:service}-${self:provider.stage}-restApiId
    
    ApiGatewayRestApiRootResourceId:
      Value:
        Fn::GetAtt:
          - ApiGatewayRestApi
          - RootResourceId
      Export:
        Name: ${self:service}-${self:provider.stage}-rootResourceId
    
    DynamoDBTableName:
      Value: ${self:custom.tableName}
      Export:
        Name: ${self:service}-${self:provider.stage}-tableName
    
    S3BucketName:
      Value: ${self:custom.bucketName}
      Export:
        Name: ${self:service}-${self:provider.stage}-bucketName
