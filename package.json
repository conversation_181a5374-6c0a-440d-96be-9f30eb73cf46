{"name": "mearoundtheworld-lambdas", "version": "1.0.0", "description": "AWS Lambda functions for MeAroundTheWorld API", "scripts": {"deploy": "serverless deploy", "deploy:dev": "serverless deploy --stage dev", "deploy:prod": "serverless deploy --stage prod", "remove": "serverless remove", "info": "serverless info", "logs": "serverless logs", "invoke": "serverless invoke", "offline": "serverless offline"}, "devDependencies": {"serverless": "^3.38.0", "serverless-python-requirements": "^6.0.0", "serverless-offline": "^13.3.0"}, "keywords": ["aws", "lambda", "serverless", "api", "travel", "photos"], "author": "MeAroundTheWorld", "license": "MIT"}