"""Lambda function for GET /users/me endpoint."""

import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from shared.lambda_utils import (
    create_response,
    create_error_response,
    require_authentication,
    handle_cors_preflight,
    async_handler,
    logger
)


@async_handler
async def lambda_handler(event, context):
    """
    AWS Lambda handler for GET /users/me endpoint.
    
    Get current authenticated user information.
    """
    try:
        # Handle CORS preflight
        cors_response = handle_cors_preflight(event)
        if cors_response:
            return cors_response
        
        # Only allow GET method
        if event.get("httpMethod") != "GET":
            return create_error_response(405, "Method not allowed")
        
        # Require authentication
        try:
            user_id = require_authentication(event)
        except ValueError:
            return create_error_response(401, "Authentication required")
        
        # Import here to avoid cold start issues
        from mearoundtheworld.repositories.user import UserRepository
        from mearoundtheworld.models.user import UserResponse
        from uuid import UUID
        
        # Get user from database
        user_repository = UserRepository()
        user = await user_repository.get_user_by_id(UUID(user_id))
        
        if not user:
            logger.error("Authenticated user not found in database", user_id=user_id)
            return create_error_response(404, "User not found")
        
        logger.debug("Current user retrieved", user_id=user_id)
        
        response_data = UserResponse.from_user(user).dict()
        return create_response(200, response_data)
        
    except Exception as e:
        logger.error("Failed to get current user", error=str(e))
        return create_error_response(500, "Failed to get user information")


# For local testing
if __name__ == "__main__":
    test_event = {
        "httpMethod": "GET",
        "headers": {
            "Authorization": "Bearer test-token"
        }
    }
    
    result = lambda_handler(test_event, None)
    print(result)
