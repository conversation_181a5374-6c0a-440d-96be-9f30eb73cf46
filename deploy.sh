#!/bin/bash

# MeAroundTheWorld Lambda Deployment Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
STAGE="dev"
REGION="us-east-1"
VERBOSE=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -s, --stage STAGE       Deployment stage (dev, prod) [default: dev]"
    echo "  -r, --region REGION     AWS region [default: us-east-1]"
    echo "  -v, --verbose           Enable verbose output"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                      Deploy to dev stage"
    echo "  $0 -s prod              Deploy to production"
    echo "  $0 -s dev -r eu-west-1  Deploy to dev in EU region"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -s|--stage)
            STAGE="$2"
            shift 2
            ;;
        -r|--region)
            REGION="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate stage
if [[ "$STAGE" != "dev" && "$STAGE" != "prod" ]]; then
    print_error "Invalid stage: $STAGE. Must be 'dev' or 'prod'"
    exit 1
fi

print_status "Starting deployment to $STAGE stage in $REGION region"

# Check prerequisites
print_status "Checking prerequisites..."

# Check if AWS CLI is installed and configured
if ! command -v aws &> /dev/null; then
    print_error "AWS CLI is not installed. Please install it first."
    exit 1
fi

# Check AWS credentials
if ! aws sts get-caller-identity &> /dev/null; then
    print_error "AWS credentials not configured. Please run 'aws configure'"
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install it first."
    exit 1
fi

# Check if Serverless Framework is installed
if ! command -v serverless &> /dev/null; then
    print_error "Serverless Framework is not installed. Please run 'npm install -g serverless'"
    exit 1
fi

print_success "Prerequisites check passed"

# Check environment variables
print_status "Checking environment variables..."

required_vars=("JWT_SECRET_KEY" "GOOGLE_CLIENT_ID" "GOOGLE_CLIENT_SECRET" "GOOGLE_REDIRECT_URI")
missing_vars=()

for var in "${required_vars[@]}"; do
    if [[ -z "${!var}" ]]; then
        missing_vars+=("$var")
    fi
done

if [[ ${#missing_vars[@]} -gt 0 ]]; then
    print_error "Missing required environment variables:"
    for var in "${missing_vars[@]}"; do
        echo "  - $var"
    done
    print_warning "Please set these variables or create a .env file"
    exit 1
fi

print_success "Environment variables check passed"

# Install Node.js dependencies
print_status "Installing Node.js dependencies..."
if [[ "$VERBOSE" == "true" ]]; then
    npm install
else
    npm install --silent
fi
print_success "Dependencies installed"

# Deploy with Serverless Framework
print_status "Deploying Lambda functions..."

deploy_cmd="serverless deploy --stage $STAGE --region $REGION"

if [[ "$VERBOSE" == "true" ]]; then
    deploy_cmd="$deploy_cmd --verbose"
fi

if eval $deploy_cmd; then
    print_success "Deployment completed successfully!"
else
    print_error "Deployment failed"
    exit 1
fi

# Get deployment info
print_status "Getting deployment information..."
serverless info --stage $STAGE --region $REGION

# Print post-deployment instructions
echo ""
print_success "Deployment Summary"
echo "=================="
echo "Stage: $STAGE"
echo "Region: $REGION"
echo "Service: mearoundtheworld-api"
echo ""
print_status "Next steps:"
echo "1. Update your frontend to use the new API Gateway endpoints"
echo "2. Test the endpoints using the provided URLs"
echo "3. Monitor the functions in AWS CloudWatch"
echo "4. Check costs in AWS Cost Explorer"
echo ""
print_status "Useful commands:"
echo "  View logs: serverless logs --function <functionName> --stage $STAGE"
echo "  Invoke function: serverless invoke --function <functionName> --stage $STAGE"
echo "  Remove deployment: serverless remove --stage $STAGE"
echo ""
print_success "Deployment completed! 🚀"
