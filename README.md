# MeAroundTheWorld AWS SAM Application

This directory contains AWS Lambda functions that replicate all the endpoints from the main backend application, designed for serverless deployment with AWS SAM (Serverless Application Model).

## Architecture

Each endpoint is implemented as a separate Lambda function using AWS SAM to provide:
- **Cost Optimization**: Pay only for actual usage
- **Scalability**: Automatic scaling based on demand
- **Isolation**: Each function runs independently
- **Local Development**: Test locally with `sam local start-api`
- **Infrastructure as Code**: Complete AWS resources defined in template.yaml

## Functions Overview

### Authentication Functions
- `auth/get_login_url.py` - GET /auth/login
- `auth/login.py` - POST /auth/login
- `auth/refresh_token.py` - POST /auth/refresh

### User Functions
- `users/get_current_user.py` - GET /users/me

### Photo Functions
- `photos/get_upload_url.py` - POST /photos/upload-url
- `photos/create_photo.py` - POST /photos
- `photos/list_photos.py` - GET /photos
- `photos/list_user_photos.py` - GET /photos/u/{user_id}
- `photos/get_photo.py` - GET /photos/{photo_id}
- `photos/update_photo.py` - PATCH /photos/{photo_id}
- `photos/delete_photo.py` - DELETE /photos/{photo_id}

### Health Functions
- `health/health_check.py` - GET /health
- `health/detailed_health.py` - GET /health/detailed

## Prerequisites

1. **AWS CLI** configured with appropriate credentials
2. **AWS SAM CLI** for local development and deployment
3. **Docker** (for local testing and building)
4. **Python 3.11** (for local testing)

```bash
# Install AWS SAM CLI
pip install aws-sam-cli

# Or using Homebrew (macOS)
brew install aws-sam-cli
```

## Environment Variables

Create a `.env` file or set these environment variables:

```bash
# Required
JWT_SECRET_KEY=your-jwt-secret-key
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=your-redirect-uri

# Optional
JWT_ALGORITHM=HS256
DEBUG=false
APP_VERSION=1.0.0
```

## Quick Start

### Initial Setup

```bash
# Setup development environment
make setup

# Or manually
./sam-dev.sh setup
```

### Local Development

```bash
# Build the application
make build

# Start local API server (http://localhost:3000)
make start

# Or using SAM directly
sam local start-api --port 3000
```

### Testing

```bash
# Run local tests
make test

# Test specific function locally
make invoke-local FUNCTION=HealthCheckFunction EVENT=events/health-check.json
```

### Deployment

```bash
# Deploy to development
make deploy-dev

# Deploy to production
make deploy-prod

# Or using SAM directly
sam deploy --config-env dev
```

## Local Testing

### API Testing
Start the local API server and test endpoints:

```bash
# Start local API
make start

# Test endpoints
curl http://localhost:3000/health
curl -X POST http://localhost:3000/auth/mock-login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "name": "Test User"}'
```

### Function Testing
Test individual functions:

```bash
# Run all tests
make test

# Test specific function
make invoke-local FUNCTION=HealthCheckFunction

# Test with custom event
sam local invoke HealthCheckFunction --event events/health-check.json
```

## Cost Optimization Features

1. **Shared Utilities**: Common code in `shared/lambda_utils.py`
2. **Lambda Layers**: Dependencies packaged in reusable layers
3. **Cold Start Optimization**: Async handler decorator and optimized imports
4. **Efficient Packaging**: SAM builds optimized deployment packages
5. **Pay-per-Request**: DynamoDB and Lambda both use pay-per-request pricing

## AWS Resources Created

- **API Gateway**: REST API with CORS enabled (defined in template.yaml)
- **Lambda Functions**: 13 functions (one per endpoint)
- **Lambda Layer**: Shared dependencies layer
- **DynamoDB Table**: Single table design with GSI and streams
- **S3 Bucket**: Photo storage with CORS and versioning
- **IAM Roles**: Minimal permissions with least privilege access
- **CloudWatch Logs**: Automatic logging for all functions

## Monitoring and Logging

- **CloudWatch Logs**: Automatic logging for all functions
- **Structured Logging**: JSON format for easy parsing
- **Error Tracking**: Detailed error responses
- **Health Checks**: Built-in health monitoring

## Security Features

- **JWT Authentication**: Secure token-based auth
- **CORS Configuration**: Proper cross-origin setup
- **IAM Permissions**: Least privilege access
- **Input Validation**: Pydantic models for data validation

## Scaling and Performance

- **Auto Scaling**: Lambda automatically scales
- **Connection Pooling**: Efficient database connections
- **Caching**: Optimized for repeated requests
- **Timeout Configuration**: Appropriate timeouts per function

## Troubleshooting

### Common Issues

1. **Docker not running**: Ensure Docker is running for local testing
2. **Import Errors**: Check Python path configuration in Lambda functions
3. **Permission Errors**: Verify IAM roles and policies in template.yaml
4. **Cold Starts**: Use async_handler decorator for better performance
5. **Environment Variables**: Ensure all required vars are set in samconfig.toml

### Debugging

```bash
# View logs
make logs FUNCTION=HealthCheckFunction

# View logs with SAM
sam logs --name HealthCheckFunction --tail

# Invoke function locally
make invoke-local FUNCTION=HealthCheckFunction EVENT=events/health-check.json

# Invoke deployed function
aws lambda invoke --function-name HealthCheckFunction response.json

# Debug with breakpoints
sam local start-api --debug-port 5858
```

### Local Development Tips

```bash
# Hot reload during development
sam local start-api --warm-containers EAGER

# Use custom Docker network
sam local start-api --docker-network my-network

# Override environment variables
sam local start-api --env-vars env.json
```

## Cost Estimation

With AWS Lambda pricing (as of 2024):
- **Requests**: $0.20 per 1M requests
- **Duration**: $0.********** per GB-second
- **Free Tier**: 1M requests and 400,000 GB-seconds per month

For a typical travel photo app:
- **Monthly Cost**: $5-20 for moderate usage
- **Scaling**: Costs scale linearly with usage
- **No Idle Costs**: Pay only when functions execute

## Migration from Container Backend

The SAM application maintains full API compatibility with the existing backend:
- Same endpoints and request/response formats
- Same authentication mechanism (JWT)
- Same data models and validation (Pydantic)
- Same business logic and database operations

### Migration Strategy

1. **Deploy SAM application** alongside existing backend
2. **Test endpoints** using local SAM API
3. **Update frontend** to use new API Gateway endpoints
4. **Monitor performance** and costs in CloudWatch
5. **Gradually migrate traffic** from container to serverless

### Advantages of SAM over Serverless Framework

- **Better local development** with `sam local start-api`
- **Integrated with AWS CLI** and CloudFormation
- **Built-in support** for API Gateway, Lambda, and other AWS services
- **Easier debugging** with local Lambda execution
- **Infrastructure as Code** with CloudFormation templates

## Next Steps

1. **Setup Environment**: `make setup`
2. **Start Local Development**: `make start`
3. **Test Endpoints**: Use curl or Postman against http://localhost:3000
4. **Deploy to AWS**: `make deploy-dev`
5. **Update Frontend**: Point API calls to new API Gateway endpoints
6. **Monitor and Optimize**: Use CloudWatch for performance monitoring

## Useful Commands

```bash
# Complete development workflow
make dev                    # setup + build + test + start

# Individual commands
make setup                  # Initial setup
make build                  # Build SAM application
make start                  # Start local API server
make test                   # Run tests
make deploy-dev            # Deploy to development
make deploy-prod           # Deploy to production
make logs FUNCTION=name    # View function logs
make clean                 # Clean build artifacts

# SAM specific commands
sam local start-api        # Start local API
sam local invoke Function  # Invoke specific function
sam deploy                 # Deploy to AWS
sam logs --tail            # Tail logs
sam delete                 # Delete stack
```
