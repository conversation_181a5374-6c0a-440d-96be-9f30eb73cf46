# MeAroundTheWorld SAM Application Makefile

.PHONY: help setup build start test deploy deploy-dev deploy-prod remove info logs clean

# Default target
help:
	@echo "Available commands:"
	@echo "  setup           - Initial setup"
	@echo "  build           - Build SAM application"
	@echo "  start           - Start local API server"
	@echo "  test            - Run all tests"
	@echo "  test-unit       - Run unit tests only"
	@echo "  test-integration- Run integration tests only"
	@echo "  test-coverage   - Run tests with coverage"
	@echo "  test-fast       - Run tests in parallel"
	@echo "  test-all        - Run all tests with coverage and parallel"
	@echo "  deploy-dev      - Deploy to development stage"
	@echo "  deploy-prod     - Deploy to production stage"
	@echo "  remove          - Remove deployment"
	@echo "  info            - Show deployment info"
	@echo "  logs            - Show function logs"
	@echo "  clean           - Clean build artifacts"

# Initial setup
setup:
	@echo "Setting up development environment..."
	./sam-dev.sh setup

# Build SAM application
build:
	@echo "Building SAM application..."
	./sam-dev.sh build

# Start local API server
start:
	@echo "Starting local API server..."
	./sam-dev.sh start

# Deploy to development
deploy-dev:
	@echo "Deploying to development stage..."
	sam deploy --config-env dev

# Deploy to production
deploy-prod:
	@echo "Deploying to production stage..."
	sam deploy --config-env prod

# Deploy with custom parameters
deploy:
	@echo "Usage: make deploy STAGE=dev"
	@if [ -z "$(STAGE)" ]; then echo "Error: STAGE is required"; exit 1; fi
	sam deploy --config-env $(STAGE)

# Remove deployment
remove:
	@echo "Removing deployment..."
	@read -p "Are you sure you want to remove the deployment? [y/N] " confirm && [ "$$confirm" = "y" ]
	sam delete --stack-name mearoundtheworld-api-$(or $(STAGE),dev)

# Show deployment info
info:
	sam list stack-outputs --stack-name mearoundtheworld-api-$(or $(STAGE),dev)

# Show function logs
logs:
	@if [ -z "$(FUNCTION)" ]; then echo "Usage: make logs FUNCTION=functionName STAGE=dev"; exit 1; fi
	./sam-dev.sh logs $(FUNCTION)

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	./sam-dev.sh cleanup
	@echo "Clean completed!"

# Quick development workflow
dev: setup build test start

# Production deployment workflow
prod: setup build test deploy-prod

# Validate environment
validate:
	@echo "Validating environment..."
	@if [ -z "$$JWT_SECRET_KEY" ]; then echo "Error: JWT_SECRET_KEY not set"; exit 1; fi
	@if [ -z "$$GOOGLE_CLIENT_ID" ]; then echo "Error: GOOGLE_CLIENT_ID not set"; exit 1; fi
	@if [ -z "$$GOOGLE_CLIENT_SECRET" ]; then echo "Error: GOOGLE_CLIENT_SECRET not set"; exit 1; fi
	@if [ -z "$$GOOGLE_REDIRECT_URI" ]; then echo "Error: GOOGLE_REDIRECT_URI not set"; exit 1; fi
	@echo "Environment validation passed!"

# Show AWS account info
aws-info:
	@echo "AWS Account Information:"
	aws sts get-caller-identity

# Invoke function locally with SAM
invoke-local:
	@if [ -z "$(FUNCTION)" ]; then echo "Usage: make invoke-local FUNCTION=functionName EVENT=events/test.json"; exit 1; fi
	sam local invoke $(FUNCTION) --event $(or $(EVENT),events/health-check.json)

# Invoke deployed function
invoke:
	@if [ -z "$(FUNCTION)" ]; then echo "Usage: make invoke FUNCTION=functionName"; exit 1; fi
	aws lambda invoke --function-name $(FUNCTION) response.json
	cat response.json

# Generate API documentation
docs:
	sam local generate-event apigateway aws-proxy --method GET --path /health > events/sample-event.json

# Tail logs in real-time
tail-logs:
	@if [ -z "$(FUNCTION)" ]; then echo "Usage: make tail-logs FUNCTION=functionName"; exit 1; fi
	sam logs --name $(FUNCTION) --tail
