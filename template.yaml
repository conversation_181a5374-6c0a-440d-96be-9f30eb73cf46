AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: MeAroundTheWorld Serverless API

Parameters:
  Stage:
    Type: String
    Default: Dev
    AllowedValues: [Dev, Prod]
    Description: Deployment stage
  
  JWTSecretKey:
    Type: String
    NoEcho: true
    Description: JWT secret key for token signing
  
  GoogleClientId:
    Type: String
    Description: Google OAuth client ID
  
  GoogleClientSecret:
    Type: String
    NoEcho: true
    Description: Google OAuth client secret
  
  GoogleRedirectUri:
    Type: String
    Description: Google OAuth redirect URI

  ApiCertificateArn:
    Type: String
    Description: ACM Certificate ARN para o domínio api.mearoundtheworld.com.br

Mappings:
  StageNames:
    Dev:
      Lower: dev
    Prod:
      Lower: prod

Globals:
  Function:
    Runtime: python3.13
    Timeout: 30
    MemorySize: 256
    Environment:
      Variables:
        STAGE: !Ref Stage
        DYNAMODB_TABLE_NAME: !Ref DynamoDBTable
        S3_BUCKET_NAME: !Ref S3Bucket
        JWT_SECRET_KEY: !Ref JWTSecretKey
        JWT_ALGORITHM: HS256
        GOOGLE_CLIENT_ID: !Ref GoogleClientId
        GOOGLE_CLIENT_SECRET: !Ref GoogleClientSecret
        GOOGLE_REDIRECT_URI: !Ref GoogleRedirectUri
        DEBUG: false
        APP_VERSION: 1.0.0
    Layers:
      - !Ref DependenciesLayer
  
  Api:
    Cors:
      AllowMethods: "'GET,POST,PUT,DELETE,PATCH,OPTIONS'"
      AllowHeaders: "'Content-Type,Authorization'"
      AllowOrigin: "'https://www.mearoundtheworld.com.br'"

Resources:
  # Dependencies Layer
  DependenciesLayer:
    Type: AWS::Serverless::LayerVersion
    Properties:
      LayerName: !Sub "${AWS::StackName}-dependencies"
      Description: Dependencies for MeAroundTheWorld API
      ContentUri: dependencies/
      CompatibleRuntimes:
        - python3.13
    Metadata:
      BuildMethod: python3.13

  ApiCustomDomain:
    Type: AWS::ApiGateway::DomainName
    Properties:
      DomainName: api.mearoundtheworld.com.br
      RegionalCertificateArn: !Ref ApiCertificateArn
      EndpointConfiguration:
        Types:
          - REGIONAL

  ApiGatewayBasePathMapping:
    Type: AWS::ApiGateway::BasePathMapping
    Properties:
      DomainName: !Ref ApiCustomDomain
      RestApiId: !Ref ServerlessRestApi
      Stage: !Ref Stage

  # Authentication Functions
  GetLoginUrlFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./
      Handler: auth.get_login_url.lambda_handler
      Runtime: python3.13
      MemorySize: 256
      Timeout: 30
      Role: !GetAtt LambdaExecutionRole.Arn
      Events:
        Api:
          Type: Api
          Properties:
            Path: /auth/login
            Method: get

  LoginFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./
      Handler: auth.login.lambda_handler
      Runtime: python3.13
      MemorySize: 256
      Timeout: 30
      Role: !GetAtt LambdaExecutionRole.Arn
      Events:
        Api:
          Type: Api
          Properties:
            Path: /auth/login
            Method: post

  RefreshTokenFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./
      Handler: auth.refresh_token.lambda_handler
      Runtime: python3.13
      MemorySize: 256
      Timeout: 30
      Role: !GetAtt LambdaExecutionRole.Arn
      Events:
        Api:
          Type: Api
          Properties:
            Path: /auth/refresh
            Method: post

  # User Functions
  GetCurrentUserFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./
      Handler: users.get_current_user.lambda_handler
      Runtime: python3.13
      MemorySize: 256
      Timeout: 30
      Role: !GetAtt LambdaExecutionRole.Arn
      Events:
        Api:
          Type: Api
          Properties:
            Path: /users/me
            Method: get

  # Photo Functions
  GetUploadUrlFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./
      Handler: photos.get_upload_url.lambda_handler
      Runtime: python3.13
      MemorySize: 256
      Timeout: 30
      Role: !GetAtt LambdaExecutionRole.Arn
      Events:
        Api:
          Type: Api
          Properties:
            Path: /photos/upload-url
            Method: post

  CreatePhotoFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./
      Handler: photos.create_photo.lambda_handler
      Runtime: python3.13
      Role: !GetAtt LambdaExecutionRole.Arn
      Events:
        Api:
          Type: Api
          Properties:
            Path: /photos
            Method: post

  ListPhotosFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./
      Handler: photos.list_photos.lambda_handler
      Runtime: python3.13
      MemorySize: 256
      Timeout: 30
      Role: !GetAtt LambdaExecutionRole.Arn
      Events:
        Api:
          Type: Api
          Properties:
            Path: /photos
            Method: get

  ListUserPhotosFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./
      Handler: photos.list_user_photos.lambda_handler
      Runtime: python3.13
      MemorySize: 256
      Timeout: 30
      Role: !GetAtt LambdaExecutionRole.Arn
      Events:
        Api:
          Type: Api
          Properties:
            Path: /photos/u/{user_id}
            Method: get

  GetPhotoFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./
      Handler: photos.get_photo.lambda_handler
      Runtime: python3.13
      MemorySize: 256
      Timeout: 30
      Role: !GetAtt LambdaExecutionRole.Arn
      Events:
        Api:
          Type: Api
          Properties:
            Path: /photos/{photo_id}
            Method: get

  UpdatePhotoFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./
      Handler: photos.update_photo.lambda_handler
      Runtime: python3.13
      MemorySize: 256
      Timeout: 30
      Role: !GetAtt LambdaExecutionRole.Arn
      Events:
        Api:
          Type: Api
          Properties:
            Path: /photos/{photo_id}
            Method: patch

  DeletePhotoFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./
      Handler: photos.delete_photo.lambda_handler
      Runtime: python3.13
      MemorySize: 256
      Timeout: 30
      Role: !GetAtt LambdaExecutionRole.Arn
      Events:
        Api:
          Type: Api
          Properties:
            Path: /photos/{photo_id}
            Method: delete

  # Health Functions
  HealthCheckFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./
      Handler: health.health_check.lambda_handler
      Runtime: python3.13
      MemorySize: 256
      Timeout: 30
      Role: !GetAtt LambdaExecutionRole.Arn
      Events:
        Api:
          Type: Api
          Properties:
            Path: /health
            Method: get

  DetailedHealthFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: ./
      Handler: health.detailed_health.lambda_handler
      Runtime: python3.13
      MemorySize: 256
      Timeout: 30
      Role: !GetAtt LambdaExecutionRole.Arn
      Events:
        Api:
          Type: Api
          Properties:
            Path: /health/detailed
            Method: get

  # DynamoDB Table
  DynamoDBTable:
    Type: AWS::DynamoDB::Table
    Properties:
      TableName: !Sub
        - "mearoundtheworld-${StageLower}"
        - { StageLower: !FindInMap [StageNames, !Ref Stage, Lower] }
      BillingMode: PAY_PER_REQUEST
      AttributeDefinitions:
        - AttributeName: pk
          AttributeType: S
        - AttributeName: sk
          AttributeType: S
        - AttributeName: gsi1pk
          AttributeType: S
        - AttributeName: gsi1sk
          AttributeType: S
      KeySchema:
        - AttributeName: pk
          KeyType: HASH
        - AttributeName: sk
          KeyType: RANGE
      GlobalSecondaryIndexes:
        - IndexName: GSI1
          KeySchema:
            - AttributeName: gsi1pk
              KeyType: HASH
            - AttributeName: gsi1sk
              KeyType: RANGE
          Projection:
            ProjectionType: ALL
      StreamSpecification:
        StreamViewType: NEW_AND_OLD_IMAGES
      PointInTimeRecoverySpecification:
        PointInTimeRecoveryEnabled: true
      Tags:
        - Key: Environment
          Value: !Ref Stage
        - Key: Service
          Value: mearoundtheworld

  # S3 Bucket
  S3Bucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub
        - "mearoundtheworld-photos-${StageLower}-${AWS::AccountId}"
        - { StageLower: !FindInMap [StageNames, !Ref Stage, Lower] }
      CorsConfiguration:
        CorsRules:
          - AllowedHeaders:
              - "*"
            AllowedMethods:
              - GET
              - PUT
              - POST
              - DELETE
              - HEAD
            AllowedOrigins:
              - "https://www.mearoundtheworld.com.br"
            MaxAge: 3000
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      VersioningConfiguration:
        Status: Enabled
      Tags:
        - Key: Environment
          Value: !Ref Stage
        - Key: Service
          Value: mearoundtheworld

  # IAM Role for Lambda Functions
  LambdaExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: DynamoDBAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:Query
                  - dynamodb:Scan
                  - dynamodb:GetItem
                  - dynamodb:PutItem
                  - dynamodb:UpdateItem
                  - dynamodb:DeleteItem
                Resource:
                  - !GetAtt DynamoDBTable.Arn
                  - !Sub "${DynamoDBTable.Arn}/index/*"
        - PolicyName: S3Access
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                  - s3:DeleteObject
                  - s3:GetObjectVersion
                Resource: !Sub "arn:aws:s3:::${S3Bucket}/*"
              - Effect: Allow
                Action:
                  - s3:ListBucket
                  - s3:HeadBucket
                Resource: !Sub "arn:aws:s3:::${S3Bucket}/*"

Outputs:
  ApiGatewayEndpoint:
    Description: "API Gateway endpoint URL"
    Value: !Sub "https://${ServerlessRestApi}.execute-api.${AWS::Region}.amazonaws.com/Prod/"
    Export:
      Name: !Sub "${AWS::StackName}-api-endpoint"

  DynamoDBTableName:
    Description: "DynamoDB table name"
    Value: !Ref DynamoDBTable
    Export:
      Name: !Sub "${AWS::StackName}-table-name"

  S3BucketName:
    Description: "S3 bucket name"
    Value: !Ref S3Bucket
    Export:
      Name: !Sub "${AWS::StackName}-bucket-name"
