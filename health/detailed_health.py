"""Lambda function for GET /health/detailed endpoint."""

import sys
import os
from datetime import datetime

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from shared.lambda_utils import (
    create_response,
    create_error_response,
    handle_cors_preflight,
    get_env_var,
    logger
)

# Lambda start time (approximation)
START_TIME = datetime.utcnow()


async def check_dynamodb():
    """Check DynamoDB connectivity."""
    try:
        import boto3
        from botocore.exceptions import ClientError
        
        dynamodb = boto3.resource('dynamodb')
        table_name = get_env_var("DYNAMODB_TABLE_NAME", "mearoundtheworld")
        
        table = dynamodb.Table(table_name)
        # Simple describe table operation
        table.load()
        
        return {"status": "healthy", "message": "DynamoDB accessible"}
    except ClientError as e:
        return {"status": "unhealthy", "error": str(e)}
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}


async def check_s3():
    """Check S3 connectivity."""
    try:
        import boto3
        from botocore.exceptions import ClientError
        
        s3 = boto3.client('s3')
        bucket_name = get_env_var("S3_BUCKET_NAME", "mearoundtheworld-photos")
        
        # Simple head bucket operation
        s3.head_bucket(Bucket=bucket_name)
        
        return {"status": "healthy", "message": "S3 accessible"}
    except ClientError as e:
        return {"status": "unhealthy", "error": str(e)}
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}


def lambda_handler(event, context):
    """
    AWS Lambda handler for GET /health/detailed endpoint.
    
    Detailed health check with dependency checks.
    """
    try:
        # Handle CORS preflight
        cors_response = handle_cors_preflight(event)
        if cors_response:
            return cors_response
        
        # Only allow GET method
        if event.get("httpMethod") != "GET":
            return create_error_response(405, "Method not allowed")
        
        current_time = datetime.utcnow()
        uptime = (current_time - START_TIME).total_seconds()
        
        # Get version from environment or default
        version = get_env_var("APP_VERSION", "1.0.0")
        
        # Perform dependency checks
        checks = {}
        overall_status = "healthy"
        
        # Check DynamoDB
        try:
            import asyncio
            dynamodb_check = asyncio.run(check_dynamodb())
            checks["dynamodb"] = dynamodb_check
            if dynamodb_check["status"] != "healthy":
                overall_status = "degraded"
        except Exception as e:
            checks["dynamodb"] = {"status": "unhealthy", "error": str(e)}
            overall_status = "degraded"
        
        # Check S3
        try:
            import asyncio
            s3_check = asyncio.run(check_s3())
            checks["s3"] = s3_check
            if s3_check["status"] != "healthy":
                overall_status = "degraded"
        except Exception as e:
            checks["s3"] = {"status": "unhealthy", "error": str(e)}
            overall_status = "degraded"
        
        response_data = {
            "status": overall_status,
            "timestamp": current_time.isoformat(),
            "version": version,
            "uptime_seconds": uptime,
            "checks": checks,
            "lambda_function": context.function_name if context else "unknown",
            "lambda_version": context.function_version if context else "unknown"
        }
        
        status_code = 200 if overall_status == "healthy" else 503
        return create_response(status_code, response_data)
        
    except Exception as e:
        logger.error("Detailed health check failed", error=str(e))
        
        response_data = {
            "status": "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": get_env_var("APP_VERSION", "1.0.0"),
            "uptime_seconds": 0,
            "checks": {"error": {"status": "unhealthy", "error": str(e)}},
            "lambda_function": context.function_name if context else "unknown",
            "lambda_version": context.function_version if context else "unknown"
        }
        
        return create_response(503, response_data)


# For local testing
if __name__ == "__main__":
    test_event = {
        "httpMethod": "GET"
    }
    
    class MockContext:
        function_name = "detailed-health-lambda"
        function_version = "1"
    
    result = lambda_handler(test_event, MockContext())
    print(result)
