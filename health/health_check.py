"""Lambda function for GET /health endpoint."""

import sys
import os
from datetime import datetime

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from shared.lambda_utils import (
    create_response,
    create_error_response,
    handle_cors_preflight,
    get_env_var,
    logger
)

# Lambda start time (approximation)
START_TIME = datetime.utcnow()


def lambda_handler(event, context):
    """
    AWS Lambda handler for GET /health endpoint.
    
    Basic health check endpoint.
    """
    try:
        # Handle CORS preflight
        cors_response = handle_cors_preflight(event)
        if cors_response:
            return cors_response
        
        # Only allow GET method
        if event.get("httpMethod") != "GET":
            return create_error_response(405, "Method not allowed")
        
        current_time = datetime.utcnow()
        uptime = (current_time - START_TIME).total_seconds()
        
        # Get version from environment or default
        version = get_env_var("APP_VERSION", "1.0.0")
        
        response_data = {
            "status": "healthy",
            "timestamp": current_time.isoformat(),
            "version": version,
            "uptime_seconds": uptime,
            "checks": {},
            "lambda_function": context.function_name if context else "unknown",
            "lambda_version": context.function_version if context else "unknown"
        }
        
        return create_response(200, response_data)
        
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        
        response_data = {
            "status": "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": get_env_var("APP_VERSION", "1.0.0"),
            "uptime_seconds": 0,
            "checks": {"error": {"status": "unhealthy", "error": str(e)}},
            "lambda_function": context.function_name if context else "unknown",
            "lambda_version": context.function_version if context else "unknown"
        }
        
        return create_response(503, response_data)


# For local testing
if __name__ == "__main__":
    test_event = {
        "httpMethod": "GET"
    }
    
    class MockContext:
        function_name = "health-check-lambda"
        function_version = "1"
    
    result = lambda_handler(test_event, MockContext())
    print(result)
