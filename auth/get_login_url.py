"""Lambda function for GET /auth/login endpoint."""

import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from shared.lambda_utils import (
    create_response,
    create_error_response,
    get_query_parameter,
    handle_cors_preflight,
    logger
)


def lambda_handler(event, context):
    """
    AWS Lambda handler for GET /auth/login endpoint.
    
    Returns Google OAuth login URL.
    """
    try:
        # Handle CORS preflight
        cors_response = handle_cors_preflight(event)
        if cors_response:
            return cors_response
        
        # Only allow GET method
        if event.get("httpMethod") != "GET":
            return create_error_response(405, "Method not allowed")
        
        # Get optional state parameter
        oauth_state = get_query_parameter(event, "oauth_state")
        
        # Import here to avoid cold start issues
        from mearoundtheworld.services.google_oauth import google_oauth_service
        
        # Generate authorization URL
        auth_url = google_oauth_service.get_authorization_url(oauth_state)
        
        logger.info("Login URL generated")
        
        response_data = {
            "authorization_url": auth_url,
            "state": oauth_state,
        }
        
        return create_response(200, response_data)
        
    except Exception as e:
        logger.error("Failed to generate login URL", error=str(e))
        return create_error_response(500, "Failed to generate login URL")


# For local testing
if __name__ == "__main__":
    test_event = {
        "httpMethod": "GET",
        "queryStringParameters": {"oauth_state": "test-state"}
    }
    
    result = lambda_handler(test_event, None)
    print(result)
