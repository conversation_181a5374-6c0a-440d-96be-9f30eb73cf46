"""Lambda function for POST /auth/login endpoint."""

import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from shared.lambda_utils import (
    create_response,
    create_error_response,
    parse_json_body,
    handle_cors_preflight,
    async_handler,
    logger
)


@async_handler
async def lambda_handler(event, context):
    """
    AWS Lambda handler for POST /auth/login endpoint.
    
    Login with Google OAuth authorization code.
    """
    try:
        # Handle CORS preflight
        cors_response = handle_cors_preflight(event)
        if cors_response:
            return cors_response
        
        # Only allow POST method
        if event.get("httpMethod") != "POST":
            return create_error_response(405, "Method not allowed")
        
        # Parse request body
        body = parse_json_body(event)
        if not body:
            return create_error_response(400, "Invalid JSON body")
        
        code = body.get("code")
        if not code:
            return create_error_response(400, "Authorization code is required")
        
        # Import here to avoid cold start issues
        from mearoundtheworld.services.google_oauth import google_oauth_service
        from mearoundtheworld.services.auth import auth_service
        from mearoundtheworld.repositories.user import UserRepository
        from mearoundtheworld.models.user import UserCreate, UserResponse
        
        # Exchange authorization code for tokens
        google_tokens = await google_oauth_service.exchange_code_for_tokens(code)
        if not google_tokens:
            logger.warning("Failed to exchange authorization code")
            return create_error_response(400, "Invalid authorization code")
        
        # Get user info from Google
        google_user = await google_oauth_service.get_user_info(google_tokens.access_token)
        if not google_user:
            logger.warning("Failed to get user info from Google")
            return create_error_response(400, "Failed to get user information")
        
        # Find or create user
        user_repository = UserRepository()
        user = await user_repository.get_user_by_email(google_user.email)
        
        logger.error("User google = %s", google_user)
        if not user:
            # Create new user
            user_data = UserCreate(
                email=google_user.email,
                name=google_user.name,
                picture_url=f"https://ui-avatars.com/api/?name={google_user.name.replace(' ', '+')}&background=6366f1&color=fff",
                google_sub=google_user.id,
            )
            user = await user_repository.create_user(user_data)
            logger.info("New user created", user_id=user.id, email=user.email)
        else:
            # Update last login
            await user_repository.update_user_login(user.id)
            logger.info("User logged in", user_id=user.id, email=user.email)
        
        # Generate JWT tokens
        tokens = auth_service.create_token_pair(user.id, user.email)
        
        response_data = {
            "user": UserResponse.from_user(user).dict(),
            "tokens": {
                "access_token": tokens.access_token,
                "refresh_token": tokens.refresh_token,
                "token_type": "bearer",
                "expires_in": auth_service.access_token_expire_minutes * 60,
            }
        }
        
        return create_response(200, response_data)
        
    except Exception as e:
        logger.error("Login failed", error=str(e))
        return create_error_response(500, "Login failed")


# For local testing
if __name__ == "__main__":
    test_event = {
        "httpMethod": "POST",
        "body": '{"code": "test-auth-code"}'
    }
    
    result = lambda_handler(test_event, None)
    print(result)
