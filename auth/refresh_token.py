"""Lambda function for POST /auth/refresh endpoint."""

import sys
import os

# Add the parent directory to the path to import shared modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from shared.lambda_utils import (
    create_response,
    create_error_response,
    parse_json_body,
    handle_cors_preflight,
    logger
)


def lambda_handler(event, context):
    """
    AWS Lambda handler for POST /auth/refresh endpoint.
    
    Refresh access token using refresh token.
    """
    try:
        # Handle CORS preflight
        cors_response = handle_cors_preflight(event)
        if cors_response:
            return cors_response
        
        # Only allow POST method
        if event.get("httpMethod") != "POST":
            return create_error_response(405, "Method not allowed")
        
        # Parse request body
        body = parse_json_body(event)
        if not body:
            return create_error_response(400, "Invalid JSON body")
        
        refresh_token = body.get("refresh_token")
        if not refresh_token:
            return create_error_response(400, "Refresh token is required")
        
        # Import here to avoid cold start issues
        from mearoundtheworld.services.auth import auth_service
        
        # Refresh access token
        new_access_token = auth_service.refresh_access_token(refresh_token)
        if not new_access_token:
            logger.warning("Invalid or expired refresh token")
            return create_error_response(401, "Invalid or expired refresh token")
        
        logger.info("Access token refreshed")
        
        response_data = {
            "access_token": new_access_token,
            "token_type": "bearer",
            "expires_in": auth_service.access_token_expire_minutes * 60,
        }
        
        return create_response(200, response_data)
        
    except Exception as e:
        logger.error("Token refresh failed", error=str(e))
        return create_error_response(500, "Token refresh failed")


# For local testing
if __name__ == "__main__":
    test_event = {
        "httpMethod": "POST",
        "body": '{"refresh_token": "test-refresh-token"}'
    }
    
    result = lambda_handler(test_event, None)
    print(result)
